<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Dashboard - MineSea">
    <title>Dashboard - MineSea</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%230066cc'/><text x='50' y='65' font-size='40' text-anchor='middle' fill='white'>🌊</text></svg>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <div class="logo-img">🌊</div>
                    <span class="logo-text">MineSea</span>
                </a>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link active">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="profile.html" class="nav-link">Profile</a>
                </li>
                <li class="nav-item">
                    <a href="leaderboard.html" class="nav-link">Leaderboard</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">Logout</a>
                </li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="welcome-banner">
                <h1>Welcome back, <span id="player-name">Player</span>!</h1>
                <p>Ready to dive into your MineSea adventure?</p>
            </div>
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="copyServerIP()">
                    <span>📋</span> Copy Server IP
                </button>
                <a href="profile.html" class="btn btn-secondary">
                    <span>👤</span> View Profile
                </a>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="dashboard-content">
        <div class="container">
            <div class="dashboard-grid">
                <!-- Server Status Card -->
                <div class="dashboard-card">
                    <h3>Server Status</h3>
                    <div class="server-status-widget" id="server-status-widget">
                        <div class="status-indicator">
                            <span class="status-dot"></span>
                            <span class="status-text">Checking...</span>
                        </div>
                        <div class="player-count">
                            <span id="online-players">--</span> / <span id="max-players">--</span> players online
                        </div>
                        <div class="server-info">
                            <p><strong>IP:</strong> play.minesea.net</p>
                            <p><strong>Version:</strong> <span id="server-version">1.20.x</span></p>
                        </div>
                    </div>
                </div>

                <!-- Player Stats Card -->
                <div class="dashboard-card">
                    <h3>Your Statistics</h3>
                    <div class="stats-widget">
                        <div class="stat-row">
                            <span class="stat-label">Playtime</span>
                            <span class="stat-value" id="user-playtime">0h</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Level</span>
                            <span class="stat-value" id="user-level">1</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Money</span>
                            <span class="stat-value" id="user-money">$0</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Rank</span>
                            <span class="stat-value" id="user-rank">#--</span>
                        </div>
                    </div>
                    <a href="profile.html" class="btn btn-secondary btn-sm">View Full Stats</a>
                </div>

                <!-- Recent Activity Card -->
                <div class="dashboard-card">
                    <h3>Recent Activity</h3>
                    <div class="activity-widget" id="recent-activity">
                        <div class="activity-item">
                            <span class="activity-icon">🎯</span>
                            <div class="activity-content">
                                <p class="activity-text">Welcome to MineSea!</p>
                                <span class="activity-time">Just now</span>
                            </div>
                        </div>
                    </div>
                    <a href="profile.html" class="btn btn-secondary btn-sm">View All Activity</a>
                </div>

                <!-- Latest News Card -->
                <div class="dashboard-card">
                    <h3>Latest News</h3>
                    <div class="news-widget" id="latest-news">
                        <!-- News items will be loaded dynamically -->
                    </div>
                    <a href="index.html#community" class="btn btn-secondary btn-sm">View All News</a>
                </div>

                <!-- Quick Links Card -->
                <div class="dashboard-card">
                    <h3>Quick Links</h3>
                    <div class="quick-links">
                        <a href="#" class="quick-link">
                            <span class="link-icon">🏆</span>
                            <span class="link-text">Achievements</span>
                        </a>
                        <a href="#" class="quick-link">
                            <span class="link-icon">👥</span>
                            <span class="link-text">Friends</span>
                        </a>
                        <a href="#" class="quick-link">
                            <span class="link-icon">💬</span>
                            <span class="link-text">Discord</span>
                        </a>
                        <a href="#" class="quick-link">
                            <span class="link-icon">📋</span>
                            <span class="link-text">Rules</span>
                        </a>
                        <a href="#" class="quick-link">
                            <span class="link-icon">🎫</span>
                            <span class="link-text">Support</span>
                        </a>
                        <a href="#" class="quick-link">
                            <span class="link-icon">⚙️</span>
                            <span class="link-text">Settings</span>
                        </a>
                    </div>
                </div>

                <!-- Online Friends Card -->
                <div class="dashboard-card">
                    <h3>Online Friends</h3>
                    <div class="friends-widget" id="online-friends">
                        <p class="no-friends">No friends online</p>
                    </div>
                    <a href="profile.html" class="btn btn-secondary btn-sm">Manage Friends</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>MineSea</h4>
                    <p>Dive into adventure with our premium Minecraft community server.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="dashboard.html">Dashboard</a></li>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="leaderboard.html">Leaderboard</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Community</h4>
                    <ul>
                        <li><a href="#">Discord</a></li>
                        <li><a href="#">Forums</a></li>
                        <li><a href="#">Rules</a></li>
                        <li><a href="#">Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MineSea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/server-status.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
