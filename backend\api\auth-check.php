<?php
/**
 * MineSea Authentication Check API
 * 
 * Checks if user is authenticated and returns user data
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../includes/auth.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['authenticated' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Check if user is authenticated
    if ($auth->isAuthenticated()) {
        $user = $auth->getCurrentUser();
        
        if ($user) {
            // Remove sensitive data
            unset($user['password_hash']);
            unset($user['email_verification_token']);
            unset($user['password_reset_token']);
            
            echo json_encode([
                'authenticated' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'minecraft_username' => $user['minecraft_username'],
                    'display_name' => $user['display_name'],
                    'role' => $user['role'],
                    'is_active' => $user['is_active'],
                    'email_verified' => $user['email_verified'],
                    'created_at' => $user['created_at'],
                    'last_login' => $user['last_login']
                ]
            ]);
        } else {
            echo json_encode(['authenticated' => false]);
        }
    } else {
        echo json_encode(['authenticated' => false]);
    }
    
} catch (Exception $e) {
    error_log("Auth check API error: " . $e->getMessage());
    echo json_encode(['authenticated' => false, 'error' => 'Internal server error']);
}

?>
