<?php
/**
 * MineSea Database Configuration
 * 
 * This file contains database connection settings and configuration
 * for the MineSea Minecraft community website.
 */

// Prevent direct access
if (!defined('MINESEA_INIT')) {
    die('Direct access not permitted');
}

// Database configuration
class DatabaseConfig {
    // Database connection settings
    const DB_HOST = 'localhost';
    const DB_NAME = 'minesea_db';
    const DB_USER = 'minesea_user';
    const DB_PASS = 'your_secure_password_here';
    const DB_CHARSET = 'utf8mb4';
    const DB_PORT = 3306;
    
    // Connection options
    const DB_OPTIONS = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    // Connection pool settings
    const MAX_CONNECTIONS = 10;
    const CONNECTION_TIMEOUT = 30;
    const RETRY_ATTEMPTS = 3;
    const RETRY_DELAY = 1; // seconds
}

/**
 * Database Connection Manager
 * Handles database connections with connection pooling and error handling
 */
class DatabaseManager {
    private static $instance = null;
    private $connection = null;
    private $connectionCount = 0;
    
    private function __construct() {
        $this->connect();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Establish database connection
     */
    private function connect() {
        $attempts = 0;
        
        while ($attempts < DatabaseConfig::RETRY_ATTEMPTS) {
            try {
                $dsn = sprintf(
                    'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                    DatabaseConfig::DB_HOST,
                    DatabaseConfig::DB_PORT,
                    DatabaseConfig::DB_NAME,
                    DatabaseConfig::DB_CHARSET
                );
                
                $this->connection = new PDO(
                    $dsn,
                    DatabaseConfig::DB_USER,
                    DatabaseConfig::DB_PASS,
                    DatabaseConfig::DB_OPTIONS
                );
                
                // Set connection timeout
                $this->connection->setAttribute(PDO::ATTR_TIMEOUT, DatabaseConfig::CONNECTION_TIMEOUT);
                
                $this->connectionCount++;
                error_log("Database connection established (attempt " . ($attempts + 1) . ")");
                return;
                
            } catch (PDOException $e) {
                $attempts++;
                error_log("Database connection failed (attempt $attempts): " . $e->getMessage());
                
                if ($attempts >= DatabaseConfig::RETRY_ATTEMPTS) {
                    throw new Exception("Failed to connect to database after " . DatabaseConfig::RETRY_ATTEMPTS . " attempts: " . $e->getMessage());
                }
                
                sleep(DatabaseConfig::RETRY_DELAY);
            }
        }
    }
    
    /**
     * Get database connection
     */
    public function getConnection() {
        // Check if connection is still alive
        if ($this->connection === null) {
            $this->connect();
        }
        
        try {
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            error_log("Database connection lost, reconnecting: " . $e->getMessage());
            $this->connect();
        }
        
        return $this->connection;
    }
    
    /**
     * Execute a prepared statement
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage() . " | SQL: " . $sql);
            throw $e;
        }
    }
    
    /**
     * Fetch single row
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get last insert ID
     */
    public function getLastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    /**
     * Check if in transaction
     */
    public function inTransaction() {
        return $this->getConnection()->inTransaction();
    }
    
    /**
     * Get connection statistics
     */
    public function getStats() {
        return [
            'connection_count' => $this->connectionCount,
            'in_transaction' => $this->inTransaction()
        ];
    }
    
    /**
     * Close connection
     */
    public function close() {
        $this->connection = null;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Database utility functions
 */
class DatabaseUtils {
    
    /**
     * Sanitize input for database queries
     */
    public static function sanitize($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitize'], $input);
        }
        
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email format
     */
    public static function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Generate secure random token
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * Hash password securely
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }
    
    /**
     * Verify password hash
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate UUID v4
     */
    public static function generateUUID() {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
        
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
    
    /**
     * Convert array to JSON safely
     */
    public static function toJson($data) {
        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    
    /**
     * Parse JSON safely
     */
    public static function fromJson($json) {
        $data = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON: ' . json_last_error_msg());
        }
        return $data;
    }
    
    /**
     * Format date for database
     */
    public static function formatDate($date = null) {
        if ($date === null) {
            $date = new DateTime();
        } elseif (is_string($date)) {
            $date = new DateTime($date);
        }
        
        return $date->format('Y-m-d H:i:s');
    }
    
    /**
     * Check if table exists
     */
    public static function tableExists($tableName) {
        $db = DatabaseManager::getInstance();
        $result = $db->fetchOne(
            "SELECT COUNT(*) as count FROM information_schema.tables 
             WHERE table_schema = ? AND table_name = ?",
            [DatabaseConfig::DB_NAME, $tableName]
        );
        
        return $result['count'] > 0;
    }
    
    /**
     * Get table row count
     */
    public static function getTableCount($tableName) {
        $db = DatabaseManager::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM `$tableName`");
        return $result['count'];
    }
    
    /**
     * Backup database table
     */
    public static function backupTable($tableName, $backupPath) {
        // This would implement table backup functionality
        // For security reasons, this is a placeholder
        throw new Exception("Backup functionality not implemented in this demo");
    }
}

// Initialize database connection on include
try {
    $db = DatabaseManager::getInstance();
    define('DB_CONNECTED', true);
} catch (Exception $e) {
    error_log("Failed to initialize database: " . $e->getMessage());
    define('DB_CONNECTED', false);
    
    // In production, you might want to show a maintenance page
    if (!defined('MAINTENANCE_MODE')) {
        http_response_code(503);
        die('Database connection failed. Please try again later.');
    }
}

?>
