<?php
/**
 * MineSea Registration API
 * 
 * Handles user registration requests
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../includes/auth.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
        exit;
    }
    
    // Validate required fields
    $requiredFields = ['username', 'email', 'minecraft_username', 'password', 'confirm_password'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => ucfirst(str_replace('_', ' ', $field)) . ' is required']);
            exit;
        }
    }
    
    // Check password confirmation
    if ($data['password'] !== $data['confirm_password']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Passwords do not match']);
        exit;
    }
    
    // Check terms acceptance
    if (!isset($data['terms']) || !$data['terms']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'You must accept the Terms of Service']);
        exit;
    }
    
    // Sanitize input
    $username = DatabaseUtils::sanitize($data['username']);
    $email = DatabaseUtils::sanitize($data['email']);
    $minecraftUsername = DatabaseUtils::sanitize($data['minecraft_username']);
    $password = $data['password']; // Don't sanitize password
    
    // Attempt registration
    $result = $auth->register($username, $email, $password, $minecraftUsername);
    
    if ($result['success']) {
        http_response_code(201);
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    error_log("Registration API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
}

?>
