<?php
/**
 * MineSea Main Configuration
 * 
 * Core configuration settings for the MineSea website
 */

// Prevent direct access
if (!defined('MINESEA_INIT')) {
    define('MINESEA_INIT', true);
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');

// Site configuration
define('SITE_NAME', 'MineSea');
define('SITE_URL', 'https://minesea.net');
define('SITE_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');

// Security settings
define('SESSION_LIFETIME', 86400); // 24 hours
define('CSRF_TOKEN_LIFETIME', 3600); // 1 hour
define('PASSWORD_RESET_LIFETIME', 1800); // 30 minutes
define('EMAIL_VERIFICATION_LIFETIME', 86400); // 24 hours

// Rate limiting
define('LOGIN_ATTEMPTS_LIMIT', 5);
define('LOGIN_ATTEMPTS_WINDOW', 900); // 15 minutes
define('API_RATE_LIMIT', 100); // requests per hour
define('CONTACT_FORM_LIMIT', 3); // submissions per hour

// File upload settings
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_PATH', __DIR__ . '/../../uploads/');

// Minecraft server settings
define('MC_SERVER_HOST', 'play.minesea.net');
define('MC_SERVER_PORT', 25565);
define('MC_QUERY_PORT', 25565);
define('MC_RCON_PORT', 25575);
define('MC_RCON_PASSWORD', 'your_rcon_password_here');

// External API settings
define('MOJANG_API_URL', 'https://api.mojang.com');
define('MINECRAFT_SERVICES_API', 'https://sessionserver.mojang.com');

// Email settings (SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');
define('SMTP_ENCRYPTION', 'tls');

// Discord webhook (optional)
define('DISCORD_WEBHOOK_URL', 'https://discord.com/api/webhooks/your_webhook_url');

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 300); // 5 minutes
define('CACHE_PATH', __DIR__ . '/../../cache/');

// Logging settings
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', __DIR__ . '/../../logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 5);

/**
 * Application Configuration Class
 */
class Config {
    private static $config = [];
    
    /**
     * Load configuration from file or database
     */
    public static function load() {
        // Load default configuration
        self::$config = [
            'site' => [
                'name' => SITE_NAME,
                'url' => SITE_URL,
                'email' => SITE_EMAIL,
                'admin_email' => ADMIN_EMAIL
            ],
            'security' => [
                'session_lifetime' => SESSION_LIFETIME,
                'csrf_token_lifetime' => CSRF_TOKEN_LIFETIME,
                'password_reset_lifetime' => PASSWORD_RESET_LIFETIME,
                'email_verification_lifetime' => EMAIL_VERIFICATION_LIFETIME,
                'login_attempts_limit' => LOGIN_ATTEMPTS_LIMIT,
                'login_attempts_window' => LOGIN_ATTEMPTS_WINDOW,
                'api_rate_limit' => API_RATE_LIMIT,
                'contact_form_limit' => CONTACT_FORM_LIMIT
            ],
            'uploads' => [
                'max_size' => MAX_UPLOAD_SIZE,
                'allowed_types' => ALLOWED_IMAGE_TYPES,
                'path' => UPLOAD_PATH
            ],
            'minecraft' => [
                'server_host' => MC_SERVER_HOST,
                'server_port' => MC_SERVER_PORT,
                'query_port' => MC_QUERY_PORT,
                'rcon_port' => MC_RCON_PORT,
                'rcon_password' => MC_RCON_PASSWORD
            ],
            'email' => [
                'smtp_host' => SMTP_HOST,
                'smtp_port' => SMTP_PORT,
                'smtp_username' => SMTP_USERNAME,
                'smtp_password' => SMTP_PASSWORD,
                'smtp_encryption' => SMTP_ENCRYPTION
            ],
            'cache' => [
                'enabled' => CACHE_ENABLED,
                'lifetime' => CACHE_LIFETIME,
                'path' => CACHE_PATH
            ],
            'logging' => [
                'enabled' => LOG_ENABLED,
                'level' => LOG_LEVEL,
                'path' => LOG_PATH,
                'max_size' => LOG_MAX_SIZE,
                'max_files' => LOG_MAX_FILES
            ]
        ];
        
        // Load additional configuration from database if available
        if (defined('DB_CONNECTED') && DB_CONNECTED) {
            self::loadFromDatabase();
        }
    }
    
    /**
     * Load configuration from database
     */
    private static function loadFromDatabase() {
        try {
            require_once __DIR__ . '/database.php';
            $db = DatabaseManager::getInstance();
            
            $settings = $db->fetchAll(
                "SELECT setting_key, setting_value, setting_type FROM server_settings WHERE is_public = 1"
            );
            
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];
                
                // Convert value based on type
                switch ($setting['setting_type']) {
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                self::$config['database'][$setting['setting_key']] = $value;
            }
        } catch (Exception $e) {
            error_log("Failed to load configuration from database: " . $e->getMessage());
        }
    }
    
    /**
     * Get configuration value
     */
    public static function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set configuration value
     */
    public static function set($key, $value) {
        $keys = explode('.', $key);
        $config = &self::$config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Check if configuration key exists
     */
    public static function has($key) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return false;
            }
            $value = $value[$k];
        }
        
        return true;
    }
    
    /**
     * Get all configuration
     */
    public static function all() {
        return self::$config;
    }
}

/**
 * Environment helper functions
 */
function env($key, $default = null) {
    $value = getenv($key);
    return $value !== false ? $value : $default;
}

function is_production() {
    return env('APP_ENV', 'development') === 'production';
}

function is_development() {
    return env('APP_ENV', 'development') === 'development';
}

function is_maintenance() {
    return defined('MAINTENANCE_MODE') && MAINTENANCE_MODE === true;
}

/**
 * Path helper functions
 */
function base_path($path = '') {
    return __DIR__ . '/../../' . ltrim($path, '/');
}

function public_path($path = '') {
    return base_path('public/' . ltrim($path, '/'));
}

function storage_path($path = '') {
    return base_path('storage/' . ltrim($path, '/'));
}

function cache_path($path = '') {
    return base_path('cache/' . ltrim($path, '/'));
}

function logs_path($path = '') {
    return base_path('logs/' . ltrim($path, '/'));
}

/**
 * URL helper functions
 */
function url($path = '') {
    return rtrim(SITE_URL, '/') . '/' . ltrim($path, '/');
}

function asset($path) {
    return url('assets/' . ltrim($path, '/'));
}

function route($name, $params = []) {
    // Simple routing helper - would be expanded in a full framework
    $routes = [
        'home' => '',
        'login' => 'login.html',
        'register' => 'register.html',
        'profile' => 'profile.html',
        'dashboard' => 'dashboard.html'
    ];
    
    if (!isset($routes[$name])) {
        return url();
    }
    
    $path = $routes[$name];
    
    // Replace parameters in route
    foreach ($params as $key => $value) {
        $path = str_replace('{' . $key . '}', $value, $path);
    }
    
    return url($path);
}

// Create necessary directories
$directories = [
    UPLOAD_PATH,
    CACHE_PATH,
    LOG_PATH
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Load configuration
Config::load();

// Include additional configuration files
require_once __DIR__ . '/database.php';

?>
