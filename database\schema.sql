-- MineSea Database Schema
-- MySQL/MariaDB Database Structure

-- Create database
CREATE DATABASE IF NOT EXISTS minesea_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE minesea_db;

-- Users table for authentication and basic user info
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    minecraft_username VARCHAR(16) NOT NULL UNIQUE,
    display_name VARCHAR(50),
    bio TEXT,
    avatar_url VARCHAR(255),
    role ENUM('player', 'moderator', 'admin') DEFAULT 'player',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_minecraft_username (minecraft_username),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
);

-- Player profiles with extended information
CREATE TABLE player_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    minecraft_uuid VARCHAR(36),
    favorite_activity ENUM('building', 'mining', 'fishing', 'exploring', 'pvp', 'redstone'),
    public_profile BOOLEAN DEFAULT TRUE,
    show_online_status BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT FALSE,
    discord_notifications BOOLEAN DEFAULT FALSE,
    discord_id VARCHAR(20),
    join_date DATE,
    last_seen TIMESTAMP NULL,
    playtime_hours INT DEFAULT 0,
    total_logins INT DEFAULT 0,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_profile (user_id),
    INDEX idx_minecraft_uuid (minecraft_uuid),
    INDEX idx_join_date (join_date),
    INDEX idx_last_seen (last_seen)
);

-- Player statistics from Minecraft server
CREATE TABLE player_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    blocks_broken INT DEFAULT 0,
    blocks_placed INT DEFAULT 0,
    distance_traveled_km DECIMAL(10,2) DEFAULT 0.00,
    mobs_killed INT DEFAULT 0,
    deaths INT DEFAULT 0,
    fish_caught INT DEFAULT 0,
    items_crafted INT DEFAULT 0,
    time_played_minutes INT DEFAULT 0,
    experience_points INT DEFAULT 0,
    level INT DEFAULT 0,
    money DECIMAL(15,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_stats (user_id),
    INDEX idx_last_updated (last_updated)
);

-- News and announcements
CREATE TABLE news (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt VARCHAR(500),
    author_id INT NOT NULL,
    category ENUM('announcement', 'update', 'event', 'maintenance') DEFAULT 'announcement',
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    
    FOREIGN KEY (author_id) REFERENCES users(id),
    INDEX idx_published (is_published, published_at),
    INDEX idx_category (category),
    INDEX idx_featured (is_featured),
    INDEX idx_created_at (created_at)
);

-- Server status logs
CREATE TABLE server_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    is_online BOOLEAN NOT NULL,
    players_online INT DEFAULT 0,
    max_players INT DEFAULT 0,
    version VARCHAR(50),
    motd TEXT,
    ping_ms INT,
    tps DECIMAL(4,2),
    memory_used_mb INT,
    memory_max_mb INT,
    cpu_usage DECIMAL(5,2),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_recorded_at (recorded_at),
    INDEX idx_is_online (is_online)
);

-- Player achievements
CREATE TABLE achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    icon VARCHAR(255),
    category ENUM('building', 'mining', 'fishing', 'exploring', 'pvp', 'social', 'special') DEFAULT 'special',
    points INT DEFAULT 10,
    is_secret BOOLEAN DEFAULT FALSE,
    requirements JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_points (points)
);

-- Player achievement progress
CREATE TABLE player_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    achievement_id INT NOT NULL,
    progress INT DEFAULT 0,
    max_progress INT DEFAULT 1,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_achievement (user_id, achievement_id),
    INDEX idx_completed (is_completed, completed_at),
    INDEX idx_user_achievements (user_id)
);

-- Friends system
CREATE TABLE friendships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    requester_id INT NOT NULL,
    addressee_id INT NOT NULL,
    status ENUM('pending', 'accepted', 'blocked') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (addressee_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_friendship (requester_id, addressee_id),
    INDEX idx_requester (requester_id),
    INDEX idx_addressee (addressee_id),
    INDEX idx_status (status)
);

-- Player activity log
CREATE TABLE player_activity (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    activity_type ENUM('login', 'logout', 'achievement', 'death', 'level_up', 'purchase') NOT NULL,
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_activity (user_id, created_at),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
);

-- Contact form submissions
CREATE TABLE contact_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email)
);

-- Sessions table for user authentication
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_sessions (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity)
);

-- Server configuration settings
CREATE TABLE server_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- Insert default achievements
INSERT INTO achievements (name, description, icon, category, points, requirements) VALUES
('Welcome to MineSea', 'Join the server for the first time', '🌊', 'special', 10, '{"type": "first_join"}'),
('First Steps', 'Walk 100 blocks', '👣', 'exploring', 5, '{"type": "distance", "amount": 100}'),
('Miner', 'Break 1000 blocks', '⛏️', 'mining', 15, '{"type": "blocks_broken", "amount": 1000}'),
('Builder', 'Place 1000 blocks', '🧱', 'building', 15, '{"type": "blocks_placed", "amount": 1000}'),
('Fisher', 'Catch 100 fish', '🎣', 'fishing', 20, '{"type": "fish_caught", "amount": 100}'),
('Explorer', 'Travel 10 kilometers', '🗺️', 'exploring', 25, '{"type": "distance_km", "amount": 10}'),
('Survivor', 'Survive 24 hours of playtime', '⏰', 'special', 30, '{"type": "playtime_hours", "amount": 24}'),
('Social Butterfly', 'Add 5 friends', '👥', 'social', 15, '{"type": "friends", "amount": 5}');

-- Insert default server settings
INSERT INTO server_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('server_name', 'MineSea', 'string', 'Server display name', TRUE),
('server_ip', 'play.minesea.net', 'string', 'Server IP address', TRUE),
('server_version', '1.20.x', 'string', 'Minecraft version', TRUE),
('max_players', '100', 'integer', 'Maximum player count', TRUE),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode status', FALSE),
('discord_invite', 'https://discord.gg/minesea', 'string', 'Discord server invite link', TRUE),
('website_title', 'MineSea - Dive into Adventure', 'string', 'Website title', TRUE);

-- Create indexes for better performance
CREATE INDEX idx_users_last_login ON users(last_login);
CREATE INDEX idx_player_profiles_playtime ON player_profiles(playtime_hours DESC);
CREATE INDEX idx_player_statistics_money ON player_statistics(money DESC);
CREATE INDEX idx_news_published_featured ON news(is_published, is_featured, published_at DESC);
CREATE INDEX idx_server_status_recent ON server_status(recorded_at DESC);
CREATE INDEX idx_player_activity_recent ON player_activity(created_at DESC);

-- Create views for common queries
CREATE VIEW active_players AS
SELECT 
    u.id,
    u.username,
    u.minecraft_username,
    u.display_name,
    pp.last_seen,
    pp.playtime_hours,
    ps.level,
    ps.money
FROM users u
JOIN player_profiles pp ON u.id = pp.user_id
JOIN player_statistics ps ON u.id = ps.user_id
WHERE u.is_active = TRUE
ORDER BY pp.last_seen DESC;

CREATE VIEW leaderboard_playtime AS
SELECT 
    u.username,
    u.minecraft_username,
    u.display_name,
    pp.playtime_hours,
    ps.level
FROM users u
JOIN player_profiles pp ON u.id = pp.user_id
JOIN player_statistics ps ON u.id = ps.user_id
WHERE u.is_active = TRUE AND pp.public_profile = TRUE
ORDER BY pp.playtime_hours DESC
LIMIT 50;

CREATE VIEW recent_news AS
SELECT 
    n.id,
    n.title,
    n.excerpt,
    n.category,
    n.published_at,
    u.display_name as author_name
FROM news n
JOIN users u ON n.author_id = u.id
WHERE n.is_published = TRUE
ORDER BY n.published_at DESC
LIMIT 10;
