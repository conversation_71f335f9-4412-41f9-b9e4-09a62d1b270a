/* MineSea - Authentication CSS */

/* Auth Page Layout */
.auth-page {
    background: var(--ocean-gradient);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.auth-page .navbar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-page .nav-logo a {
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-page .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.auth-page .nav-link:hover {
    color: var(--text-light);
}

/* Auth Container */
.auth-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.auth-background .wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".1" fill="%23ffffff"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".2" fill="%23ffffff"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" opacity=".3" fill="%23ffffff"></path></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: wave 15s linear infinite;
}

.auth-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 450px;
}

/* Auth Card */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h1 {
    color: var(--primary-blue);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Auth Form */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--background-light);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-group input:invalid {
    border-color: #e74c3c;
}

.form-group input:valid {
    border-color: #27ae60;
}

/* Form Error and Hint Messages */
.form-error {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: none;
}

.form-error.show {
    display: block;
}

.form-hint {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Custom Checkbox */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-link {
    color: var(--primary-blue);
    font-size: 0.9rem;
    text-decoration: none;
    transition: var(--transition-fast);
}

.forgot-link:hover {
    text-decoration: underline;
}

/* Auth Divider */
.auth-divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.auth-divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
}

.auth-footer p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.auth-footer a {
    color: var(--primary-blue);
    font-weight: 500;
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Loading State */
.auth-form.loading .btn {
    position: relative;
    color: transparent;
}

.auth-form.loading .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #c3e6cb;
    margin-bottom: 1rem;
    display: none;
}

.success-message.show {
    display: block;
}

/* Error Message */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #f5c6cb;
    margin-bottom: 1rem;
    display: none;
}

.error-message.show {
    display: block;
}

/* Responsive Auth Styles */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem;
        border-radius: 16px;
    }
    
    .auth-header h1 {
        font-size: 2rem;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem;
        margin: 0.5rem;
    }
    
    .auth-header h1 {
        font-size: 1.75rem;
    }
    
    .form-group input {
        padding: 10px 14px;
    }
}

/* High Contrast Mode for Auth */
@media (prefers-contrast: high) {
    .auth-card {
        background: #ffffff;
        border: 3px solid #000000;
    }
    
    .form-group input {
        border-width: 3px;
    }
    
    .checkmark {
        border-width: 3px;
    }
}

/* Dark Mode for Auth */
@media (prefers-color-scheme: dark) {
    .auth-card {
        background: rgba(42, 42, 42, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .auth-header h1 {
        color: var(--accent-teal);
    }
    
    .form-group input {
        background: var(--background-gray);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-group input::placeholder {
        color: var(--text-secondary);
    }
    
    .auth-divider span {
        background: rgba(42, 42, 42, 0.95);
    }
}
