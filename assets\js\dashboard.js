// MineSea - Dashboard JavaScript

class DashboardManager {
    constructor() {
        this.user = null;
        this.updateInterval = 60000; // 1 minute
        this.intervalIds = [];
        
        this.init();
    }
    
    async init() {
        // Check authentication
        await this.checkAuth();
        
        // Load dashboard data
        await this.loadDashboardData();
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    async checkAuth() {
        try {
            const response = await fetch('backend/api/auth-check.php');
            const result = await response.json();
            
            if (!result.authenticated) {
                // Redirect to login if not authenticated
                window.location.href = 'login.html';
                return;
            }
            
            this.user = result.user;
            this.updateUserInfo();
            
        } catch (error) {
            console.error('Auth check failed:', error);
            window.location.href = 'login.html';
        }
    }
    
    updateUserInfo() {
        if (!this.user) return;
        
        // Update player name in welcome banner
        const playerNameElement = document.getElementById('player-name');
        if (playerNameElement) {
            playerNameElement.textContent = this.user.display_name || this.user.username;
        }
    }
    
    async loadDashboardData() {
        // Load all dashboard components
        await Promise.all([
            this.loadPlayerStats(),
            this.loadRecentActivity(),
            this.loadLatestNews(),
            this.loadOnlineFriends()
        ]);
    }
    
    async loadPlayerStats() {
        try {
            const response = await fetch(`backend/api/player-stats.php?user_id=${this.user.id}`);
            const result = await response.json();
            
            if (result.success) {
                this.updatePlayerStats(result.data);
            } else {
                // Show default stats
                this.updatePlayerStats({
                    playtime_hours: 0,
                    level: 1,
                    money: 0,
                    rank: '--'
                });
            }
        } catch (error) {
            console.error('Failed to load player stats:', error);
            this.updatePlayerStats({
                playtime_hours: 0,
                level: 1,
                money: 0,
                rank: '--'
            });
        }
    }
    
    updatePlayerStats(stats) {
        const elements = {
            'user-playtime': `${stats.playtime_hours || 0}h`,
            'user-level': stats.level || 1,
            'user-money': `$${(stats.money || 0).toLocaleString()}`,
            'user-rank': stats.rank ? `#${stats.rank}` : '#--'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    async loadRecentActivity() {
        try {
            const response = await fetch(`backend/api/player-activity.php?user_id=${this.user.id}&limit=5`);
            const result = await response.json();
            
            if (result.success && result.data.length > 0) {
                this.updateRecentActivity(result.data);
            } else {
                // Show welcome message for new users
                this.updateRecentActivity([
                    {
                        activity_type: 'welcome',
                        description: 'Welcome to MineSea!',
                        created_at: new Date().toISOString()
                    }
                ]);
            }
        } catch (error) {
            console.error('Failed to load recent activity:', error);
        }
    }
    
    updateRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;
        
        const activityHTML = activities.map(activity => {
            const icon = this.getActivityIcon(activity.activity_type);
            const timeAgo = this.getTimeAgo(activity.created_at);
            
            return `
                <div class="activity-item">
                    <span class="activity-icon">${icon}</span>
                    <div class="activity-content">
                        <p class="activity-text">${this.escapeHtml(activity.description)}</p>
                        <span class="activity-time">${timeAgo}</span>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = activityHTML;
    }
    
    async loadLatestNews() {
        try {
            const response = await fetch('backend/api/news.php?limit=3');
            const result = await response.json();
            
            if (result.success) {
                this.updateLatestNews(result.data);
            }
        } catch (error) {
            console.error('Failed to load latest news:', error);
        }
    }
    
    updateLatestNews(news) {
        const container = document.getElementById('latest-news');
        if (!container) return;
        
        if (news.length === 0) {
            container.innerHTML = '<p class="no-news">No news available</p>';
            return;
        }
        
        const newsHTML = news.map(item => {
            const timeAgo = this.getTimeAgo(item.published_at);
            
            return `
                <div class="news-item-small">
                    <h4 class="news-title">${this.escapeHtml(item.title)}</h4>
                    <p class="news-excerpt">${this.escapeHtml(item.excerpt)}</p>
                    <span class="news-time">${timeAgo}</span>
                </div>
            `;
        }).join('');
        
        container.innerHTML = newsHTML;
    }
    
    async loadOnlineFriends() {
        try {
            const response = await fetch(`backend/api/friends.php?user_id=${this.user.id}&online_only=true`);
            const result = await response.json();
            
            if (result.success) {
                this.updateOnlineFriends(result.data);
            }
        } catch (error) {
            console.error('Failed to load online friends:', error);
        }
    }
    
    updateOnlineFriends(friends) {
        const container = document.getElementById('online-friends');
        if (!container) return;
        
        if (friends.length === 0) {
            container.innerHTML = '<p class="no-friends">No friends online</p>';
            return;
        }
        
        const friendsHTML = friends.map(friend => `
            <div class="friend-item">
                <img src="https://mc-heads.net/avatar/${friend.minecraft_username}/24" 
                     alt="${friend.username}" 
                     class="friend-avatar"
                     onerror="this.src='assets/images/default-avatar.png'">
                <span class="friend-name">${this.escapeHtml(friend.display_name || friend.username)}</span>
                <span class="friend-status online">●</span>
            </div>
        `).join('');
        
        container.innerHTML = friendsHTML;
    }
    
    getActivityIcon(activityType) {
        const icons = {
            'login': '🚪',
            'logout': '👋',
            'achievement': '🏆',
            'death': '💀',
            'level_up': '⬆️',
            'purchase': '💰',
            'welcome': '🌊'
        };
        
        return icons[activityType] || '📝';
    }
    
    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    }
    
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    
    startPeriodicUpdates() {
        // Update player stats every minute
        const statsInterval = setInterval(() => {
            this.loadPlayerStats();
        }, this.updateInterval);
        
        // Update recent activity every 2 minutes
        const activityInterval = setInterval(() => {
            this.loadRecentActivity();
        }, this.updateInterval * 2);
        
        // Update online friends every 30 seconds
        const friendsInterval = setInterval(() => {
            this.loadOnlineFriends();
        }, 30000);
        
        this.intervalIds.push(statsInterval, activityInterval, friendsInterval);
    }
    
    setupEventListeners() {
        // Handle visibility change to pause/resume updates
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseUpdates();
            } else {
                this.resumeUpdates();
            }
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    pauseUpdates() {
        this.intervalIds.forEach(id => clearInterval(id));
        this.intervalIds = [];
    }
    
    resumeUpdates() {
        if (this.intervalIds.length === 0) {
            this.startPeriodicUpdates();
        }
    }
    
    cleanup() {
        this.pauseUpdates();
    }
    
    // Manual refresh function
    async refresh() {
        await this.loadDashboardData();
        
        if (window.MineSea && window.MineSea.showNotification) {
            window.MineSea.showNotification('Dashboard refreshed!', 'success');
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.dashboardManager = new DashboardManager();
});

// Add refresh functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcut for refresh (Ctrl+R or F5)
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
            e.preventDefault();
            if (window.dashboardManager) {
                window.dashboardManager.refresh();
            }
        }
    });
});

// Export for use in other scripts
window.DashboardManager = DashboardManager;
