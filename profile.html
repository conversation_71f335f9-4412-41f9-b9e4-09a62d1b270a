<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Player Profile - MineSea">
    <title>Profile - MineSea</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/profile.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%230066cc'/><text x='50' y='65' font-size='40' text-anchor='middle' fill='white'>🌊</text></svg>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <div class="logo-img">🌊</div>
                    <span class="logo-text">MineSea</span>
                </a>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="profile.html" class="nav-link active">Profile</a>
                </li>
                <li class="nav-item">
                    <a href="leaderboard.html" class="nav-link">Leaderboard</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">Logout</a>
                </li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Profile Header -->
    <section class="profile-header">
        <div class="container">
            <div class="profile-banner">
                <div class="profile-avatar">
                    <div class="avatar-placeholder" id="player-avatar">
                        <span class="avatar-icon">👤</span>
                    </div>
                    <div class="avatar-overlay">
                        <button class="btn btn-secondary btn-sm" onclick="changeAvatar()">Change Avatar</button>
                    </div>
                </div>
                <div class="profile-info">
                    <h1 class="player-name" id="player-name">Loading...</h1>
                    <p class="player-title" id="player-title">Member</p>
                    <div class="player-stats">
                        <div class="stat">
                            <span class="stat-value" id="playtime">0h</span>
                            <span class="stat-label">Playtime</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value" id="join-date">--</span>
                            <span class="stat-label">Joined</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value" id="last-seen">--</span>
                            <span class="stat-label">Last Seen</span>
                        </div>
                    </div>
                </div>
                <div class="profile-actions">
                    <button class="btn btn-primary" onclick="editProfile()">Edit Profile</button>
                    <button class="btn btn-secondary" onclick="viewAchievements()">Achievements</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="profile-content">
        <div class="container">
            <div class="profile-grid">
                <!-- Player Statistics -->
                <div class="profile-card">
                    <h3>Player Statistics</h3>
                    <div class="stats-list">
                        <div class="stat-item">
                            <span class="stat-name">Blocks Broken</span>
                            <span class="stat-value" id="blocks-broken">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-name">Blocks Placed</span>
                            <span class="stat-value" id="blocks-placed">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-name">Distance Traveled</span>
                            <span class="stat-value" id="distance-traveled">0 km</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-name">Mobs Killed</span>
                            <span class="stat-value" id="mobs-killed">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-name">Deaths</span>
                            <span class="stat-value" id="deaths">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-name">Fish Caught</span>
                            <span class="stat-value" id="fish-caught">0</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Achievements -->
                <div class="profile-card">
                    <h3>Recent Achievements</h3>
                    <div class="achievements-list" id="achievements-list">
                        <!-- Achievements will be loaded dynamically -->
                    </div>
                    <button class="btn btn-secondary btn-sm" onclick="viewAllAchievements()">View All</button>
                </div>

                <!-- Player Inventory -->
                <div class="profile-card">
                    <h3>Inventory Highlights</h3>
                    <div class="inventory-grid" id="inventory-grid">
                        <!-- Inventory items will be loaded dynamically -->
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="profile-card">
                    <h3>Recent Activity</h3>
                    <div class="activity-list" id="activity-list">
                        <!-- Activity will be loaded dynamically -->
                    </div>
                </div>

                <!-- Player Settings -->
                <div class="profile-card">
                    <h3>Settings</h3>
                    <div class="settings-list">
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="public-profile" checked>
                                <span class="checkmark"></span>
                                Public Profile
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="show-online-status" checked>
                                <span class="checkmark"></span>
                                Show Online Status
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="email-notifications">
                                <span class="checkmark"></span>
                                Email Notifications
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="discord-notifications">
                                <span class="checkmark"></span>
                                Discord Notifications
                            </label>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="saveSettings()">Save Settings</button>
                </div>

                <!-- Friends List -->
                <div class="profile-card">
                    <h3>Friends</h3>
                    <div class="friends-list" id="friends-list">
                        <!-- Friends will be loaded dynamically -->
                    </div>
                    <button class="btn btn-secondary btn-sm" onclick="manageFriends()">Manage Friends</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Edit Profile Modal -->
    <div class="modal" id="edit-profile-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Profile</h3>
                <button class="modal-close" onclick="closeModal('edit-profile-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="edit-profile-form">
                    <div class="form-group">
                        <label for="display-name">Display Name</label>
                        <input type="text" id="display-name" name="display_name" maxlength="20">
                    </div>
                    <div class="form-group">
                        <label for="bio">Bio</label>
                        <textarea id="bio" name="bio" rows="3" maxlength="200" placeholder="Tell us about yourself..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="favorite-activity">Favorite Activity</label>
                        <select id="favorite-activity" name="favorite_activity">
                            <option value="">Select an activity</option>
                            <option value="building">Building</option>
                            <option value="mining">Mining</option>
                            <option value="fishing">Fishing</option>
                            <option value="exploring">Exploring</option>
                            <option value="pvp">PvP</option>
                            <option value="redstone">Redstone</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('edit-profile-modal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>MineSea</h4>
                    <p>Dive into adventure with our premium Minecraft community server.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="dashboard.html">Dashboard</a></li>
                        <li><a href="leaderboard.html">Leaderboard</a></li>
                        <li><a href="support.html">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Community</h4>
                    <ul>
                        <li><a href="#">Discord</a></li>
                        <li><a href="#">Forums</a></li>
                        <li><a href="#">Rules</a></li>
                        <li><a href="#">Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MineSea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/profile.js"></script>
</body>
</html>
