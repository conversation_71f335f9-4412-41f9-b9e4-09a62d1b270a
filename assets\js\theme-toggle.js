// MineSea - Dark Mode Toggle Functionality

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.toggleButton = null;
        this.storageKey = 'minesea-theme';
        
        this.init();
    }
    
    init() {
        // Get saved theme or detect system preference
        this.currentTheme = this.getSavedTheme() || this.getSystemTheme();
        
        // Apply theme immediately to prevent flash
        this.applyTheme(this.currentTheme);
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupToggle());
        } else {
            this.setupToggle();
        }
        
        // Listen for system theme changes
        this.watchSystemTheme();
    }
    
    setupToggle() {
        this.toggleButton = document.getElementById('theme-toggle');
        
        if (this.toggleButton) {
            this.toggleButton.addEventListener('click', () => this.toggleTheme());
            
            // Update button state
            this.updateToggleButton();
        }
    }
    
    getSavedTheme() {
        try {
            return localStorage.getItem(this.storageKey);
        } catch (error) {
            console.warn('Unable to access localStorage for theme preference');
            return null;
        }
    }
    
    getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    saveTheme(theme) {
        try {
            localStorage.setItem(this.storageKey, theme);
        } catch (error) {
            console.warn('Unable to save theme preference to localStorage');
        }
    }
    
    applyTheme(theme) {
        this.currentTheme = theme;
        
        // Apply theme to document
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.removeAttribute('data-theme');
        }
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(theme);
        
        // Save preference
        this.saveTheme(theme);
        
        // Update toggle button if available
        if (this.toggleButton) {
            this.updateToggleButton();
        }
        
        // Dispatch custom event for other components
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        
        // Add a subtle animation to the toggle button
        if (this.toggleButton) {
            this.toggleButton.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.toggleButton.style.transform = '';
            }, 150);
        }
    }
    
    updateToggleButton() {
        if (!this.toggleButton) return;
        
        const isDark = this.currentTheme === 'dark';
        this.toggleButton.setAttribute('aria-label', 
            isDark ? 'Switch to light mode' : 'Switch to dark mode'
        );
    }
    
    updateMetaThemeColor(theme) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        // Set appropriate theme color based on current theme
        const colors = {
            light: '#ffffff',
            dark: '#1a202c'
        };
        
        metaThemeColor.content = colors[theme];
    }
    
    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!this.getSavedTheme()) {
                    const systemTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme(systemTheme);
                }
            });
        }
    }
    
    // Public methods for external use
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    setTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.applyTheme(theme);
        }
    }
    
    // Method to check if dark mode is active
    isDarkMode() {
        return this.currentTheme === 'dark';
    }
    
    // Method to reset to system preference
    resetToSystem() {
        try {
            localStorage.removeItem(this.storageKey);
            const systemTheme = this.getSystemTheme();
            this.applyTheme(systemTheme);
        } catch (error) {
            console.warn('Unable to reset theme preference');
        }
    }
}

// Initialize theme manager immediately to prevent flash
const themeManager = new ThemeManager();

// Export for use in other scripts
window.ThemeManager = ThemeManager;
window.themeManager = themeManager;

// Utility functions for other scripts
window.isDarkMode = () => themeManager.isDarkMode();
window.toggleTheme = () => themeManager.toggleTheme();
window.setTheme = (theme) => themeManager.setTheme(theme);

// Listen for theme changes to update other components
document.addEventListener('themeChanged', (event) => {
    const { theme } = event.detail;
    
    // Update any dynamic content that needs theme-specific styling
    const dynamicElements = document.querySelectorAll('[data-theme-dynamic]');
    dynamicElements.forEach(element => {
        element.classList.toggle('dark-theme', theme === 'dark');
    });
    
    // Update charts, graphs, or other visual elements if needed
    if (window.updateChartsTheme) {
        window.updateChartsTheme(theme);
    }
});

// Keyboard shortcut for theme toggle (Ctrl/Cmd + Shift + D)
document.addEventListener('keydown', (event) => {
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        themeManager.toggleTheme();
    }
});

// Expose theme utilities globally
window.MineSea = window.MineSea || {};
window.MineSea.theme = {
    toggle: () => themeManager.toggleTheme(),
    set: (theme) => themeManager.setTheme(theme),
    get: () => themeManager.getCurrentTheme(),
    isDark: () => themeManager.isDarkMode(),
    reset: () => themeManager.resetToSystem()
};
