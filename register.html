<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Register for MineSea - Premium Minecraft Community Server">
    <title>Register - MineSea</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body class="auth-page">
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">
                    <img src="assets/images/logo.png" alt="MineSea Logo" class="logo-img">
                    <span class="logo-text">MineSea</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Back to Home</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Auth Container -->
    <div class="auth-container">
        <div class="auth-background">
            <div class="wave-animation"></div>
        </div>
        
        <div class="auth-content">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>Join MineSea</h1>
                    <p>Create your account and start your adventure</p>
                </div>

                <form class="auth-form" id="register-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                        <span class="form-error" id="username-error"></span>
                        <span class="form-hint">3-16 characters, letters and numbers only</span>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" required>
                        <span class="form-error" id="email-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="minecraft-username">Minecraft Username</label>
                        <input type="text" id="minecraft-username" name="minecraft_username" required>
                        <span class="form-error" id="minecraft-username-error"></span>
                        <span class="form-hint">Your in-game Minecraft username</span>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                        <span class="form-error" id="password-error"></span>
                        <span class="form-hint">At least 8 characters with letters and numbers</span>
                    </div>

                    <div class="form-group">
                        <label for="confirm-password">Confirm Password</label>
                        <input type="password" id="confirm-password" name="confirm_password" required>
                        <span class="form-error" id="confirm-password-error"></span>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="terms" name="terms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                </form>

                <div class="auth-divider">
                    <span>or</span>
                </div>

                <div class="auth-footer">
                    <p>Already have an account? <a href="login.html">Sign in here</a></p>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/auth.js"></script>
</body>
</html>
