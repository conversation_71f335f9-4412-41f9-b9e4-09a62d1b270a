// MineSea - Main JavaScript

// Global variables
let isLoggedIn = false;
let currentUser = null;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkAuthStatus();
});

// Initialize Application
function initializeApp() {
    // Initialize navigation
    initializeNavigation();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
    
    // Initialize animations
    initializeAnimations();
    
    // Load server status
    loadServerStatus();
    
    // Load news if on homepage
    if (document.getElementById('news-list')) {
        loadNews();
    }
    
    // Initialize contact form if present
    if (document.getElementById('contact-form')) {
        initializeContactForm();
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Mobile navigation toggle
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navMenu && navMenu.classList.contains('active')) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });
    
    // Window scroll events
    window.addEventListener('scroll', handleScroll);
    
    // Window resize events
    window.addEventListener('resize', handleResize);
}

// Navigation Functions
function initializeNavigation() {
    const navbar = document.getElementById('navbar');
    if (!navbar) return;
    
    // Add scroll effect to navbar
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Smooth Scrolling
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Animation Functions
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .stat-item, .news-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Counter animation for stats
    animateCounters();
}

// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number[data-target]');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        // Start animation when element is visible
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(counter);
    });
}

// Server Status Functions
async function loadServerStatus() {
    const statusElement = document.getElementById('server-status');
    if (!statusElement) return;
    
    try {
        const response = await fetch('backend/api/server-status.php');
        const data = await response.json();
        
        updateServerStatus(data);
    } catch (error) {
        console.error('Error loading server status:', error);
        updateServerStatus({
            online: false,
            players: { online: 0, max: 0 }
        });
    }
}

function updateServerStatus(data) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');
    const onlinePlayers = document.getElementById('online-players');
    const maxPlayers = document.getElementById('max-players');
    
    if (data.online) {
        statusDot.style.backgroundColor = '#4caf50';
        statusText.textContent = 'Server Online';
        onlinePlayers.textContent = data.players.online;
        maxPlayers.textContent = data.players.max;
    } else {
        statusDot.style.backgroundColor = '#f44336';
        statusText.textContent = 'Server Offline';
        onlinePlayers.textContent = '--';
        maxPlayers.textContent = '--';
    }
}

// News Functions
async function loadNews() {
    const newsList = document.getElementById('news-list');
    if (!newsList) return;
    
    try {
        const response = await fetch('backend/api/news.php');
        const news = await response.json();
        
        displayNews(news);
    } catch (error) {
        console.error('Error loading news:', error);
        newsList.innerHTML = '<p>Unable to load news at this time.</p>';
    }
}

function displayNews(news) {
    const newsList = document.getElementById('news-list');
    
    if (news.length === 0) {
        newsList.innerHTML = '<p>No news available.</p>';
        return;
    }
    
    const newsHTML = news.map(item => `
        <div class="news-item">
            <div class="news-date">${formatDate(item.created_at)}</div>
            <div class="news-title">${escapeHtml(item.title)}</div>
            <div class="news-excerpt">${escapeHtml(item.excerpt)}</div>
        </div>
    `).join('');
    
    newsList.innerHTML = newsHTML;
}

// Contact Form
function initializeContactForm() {
    const contactForm = document.getElementById('contact-form');
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        
        // Disable submit button
        submitButton.disabled = true;
        submitButton.textContent = 'Sending...';
        
        try {
            const response = await fetch('backend/api/contact.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification('Message sent successfully!', 'success');
                contactForm.reset();
            } else {
                showNotification(result.message || 'Failed to send message.', 'error');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            showNotification('Failed to send message. Please try again.', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = 'Send Message';
        }
    });
}

// Server IP Copy Function
function copyServerIP() {
    const serverIP = 'play.minesea.net';
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(serverIP).then(() => {
            showNotification('Server IP copied to clipboard!', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(serverIP);
        });
    } else {
        fallbackCopyTextToClipboard(serverIP);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showNotification('Server IP copied to clipboard!', 'success');
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Unable to copy. Please copy manually: ' + text, 'error');
    }
    
    document.body.removeChild(textArea);
}

// Authentication Functions
async function checkAuthStatus() {
    try {
        const response = await fetch('backend/api/auth-check.php');
        const data = await response.json();
        
        if (data.authenticated) {
            isLoggedIn = true;
            currentUser = data.user;
            updateUIForLoggedInUser();
        } else {
            isLoggedIn = false;
            currentUser = null;
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        console.error('Error checking auth status:', error);
    }
}

function updateUIForLoggedInUser() {
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.textContent = 'Dashboard';
        loginBtn.href = 'dashboard.html';
    }
}

function updateUIForLoggedOutUser() {
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.textContent = 'Login';
        loginBtn.href = 'login.html';
    }
}

// Utility Functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Event Handlers
function handleScroll() {
    // Add any scroll-based functionality here
}

function handleResize() {
    // Add any resize-based functionality here
}

// Export functions for use in other scripts
window.MineSea = {
    showNotification,
    copyServerIP,
    formatDate,
    escapeHtml,
    checkAuthStatus,
    loadServerStatus
};
