<?php
/**
 * MineSea Server Status API
 * 
 * Provides real-time Minecraft server status information
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

/**
 * Minecraft Server Query Class
 */
class MinecraftServerQuery {
    private $host;
    private $port;
    private $timeout;
    
    public function __construct($host, $port = 25565, $timeout = 5) {
        $this->host = $host;
        $this->port = $port;
        $this->timeout = $timeout;
    }
    
    /**
     * Get server status using Server List Ping
     */
    public function getStatus() {
        try {
            $startTime = microtime(true);
            
            // Create socket connection
            $socket = @fsockopen($this->host, $this->port, $errno, $errstr, $this->timeout);
            
            if (!$socket) {
                return $this->getOfflineStatus();
            }
            
            // Set socket timeout
            stream_set_timeout($socket, $this->timeout);
            
            // Send handshake packet
            $this->writeVarInt($socket, 0x00); // Packet ID
            $this->writeVarInt($socket, 47);   // Protocol version (1.8.x)
            $this->writeString($socket, $this->host);
            $this->writeShort($socket, $this->port);
            $this->writeVarInt($socket, 1);    // Next state (status)
            
            // Send status request
            $this->writeVarInt($socket, 0x00); // Packet ID
            
            // Read response
            $length = $this->readVarInt($socket);
            $packetId = $this->readVarInt($socket);
            $jsonLength = $this->readVarInt($socket);
            $json = fread($socket, $jsonLength);
            
            fclose($socket);
            
            $endTime = microtime(true);
            $ping = round(($endTime - $startTime) * 1000);
            
            $data = json_decode($json, true);
            
            if (!$data) {
                return $this->getOfflineStatus();
            }
            
            return $this->formatStatusResponse($data, $ping);
            
        } catch (Exception $e) {
            error_log("Server status query error: " . $e->getMessage());
            return $this->getOfflineStatus();
        }
    }
    
    /**
     * Format status response
     */
    private function formatStatusResponse($data, $ping) {
        $players = $data['players'] ?? [];
        $version = $data['version'] ?? [];
        
        return [
            'online' => true,
            'players' => [
                'online' => $players['online'] ?? 0,
                'max' => $players['max'] ?? 0,
                'list' => $this->formatPlayerList($players['sample'] ?? [])
            ],
            'version' => $version['name'] ?? 'Unknown',
            'protocol' => $version['protocol'] ?? 0,
            'motd' => $this->cleanMotd($data['description'] ?? ''),
            'ping' => $ping,
            'favicon' => $data['favicon'] ?? null,
            'timestamp' => time()
        ];
    }
    
    /**
     * Format player list
     */
    private function formatPlayerList($players) {
        $formatted = [];
        
        foreach ($players as $player) {
            $formatted[] = [
                'name' => $player['name'] ?? 'Unknown',
                'uuid' => $player['id'] ?? ''
            ];
        }
        
        return $formatted;
    }
    
    /**
     * Clean MOTD from formatting codes
     */
    private function cleanMotd($motd) {
        if (is_array($motd)) {
            $motd = $motd['text'] ?? '';
        }
        
        // Remove Minecraft color codes
        $motd = preg_replace('/§[0-9a-fk-or]/', '', $motd);
        
        return trim($motd);
    }
    
    /**
     * Get offline status response
     */
    private function getOfflineStatus() {
        return [
            'online' => false,
            'players' => [
                'online' => 0,
                'max' => 0,
                'list' => []
            ],
            'version' => 'Unknown',
            'protocol' => 0,
            'motd' => 'Server Offline',
            'ping' => 0,
            'favicon' => null,
            'timestamp' => time()
        ];
    }
    
    /**
     * Write variable-length integer
     */
    private function writeVarInt($socket, $value) {
        $bytes = '';
        
        do {
            $temp = $value & 0x7F;
            $value >>= 7;
            
            if ($value != 0) {
                $temp |= 0x80;
            }
            
            $bytes .= chr($temp);
        } while ($value != 0);
        
        fwrite($socket, $bytes);
    }
    
    /**
     * Read variable-length integer
     */
    private function readVarInt($socket) {
        $value = 0;
        $position = 0;
        
        do {
            $byte = ord(fread($socket, 1));
            $value |= ($byte & 0x7F) << $position;
            
            if (($byte & 0x80) == 0) {
                break;
            }
            
            $position += 7;
            
            if ($position >= 32) {
                throw new Exception("VarInt is too big");
            }
        } while (true);
        
        return $value;
    }
    
    /**
     * Write string
     */
    private function writeString($socket, $string) {
        $this->writeVarInt($socket, strlen($string));
        fwrite($socket, $string);
    }
    
    /**
     * Write short
     */
    private function writeShort($socket, $value) {
        fwrite($socket, pack('n', $value));
    }
}

/**
 * Server Status Manager
 */
class ServerStatusManager {
    private $db;
    private $cacheFile;
    private $cacheLifetime;
    
    public function __construct() {
        $this->db = DatabaseManager::getInstance();
        $this->cacheFile = cache_path('server_status.json');
        $this->cacheLifetime = Config::get('cache.lifetime', 300); // 5 minutes
    }
    
    /**
     * Get server status with caching
     */
    public function getStatus() {
        // Try to get from cache first
        $cachedStatus = $this->getCachedStatus();
        if ($cachedStatus) {
            return $cachedStatus;
        }
        
        // Query server
        $query = new MinecraftServerQuery(
            Config::get('minecraft.server_host'),
            Config::get('minecraft.server_port', 25565)
        );
        
        $status = $query->getStatus();
        
        // Add performance metrics if available
        $status['performance'] = $this->getPerformanceMetrics();
        
        // Cache the result
        $this->cacheStatus($status);
        
        // Store in database
        $this->storeStatusInDatabase($status);
        
        return $status;
    }
    
    /**
     * Get cached status
     */
    private function getCachedStatus() {
        if (!file_exists($this->cacheFile)) {
            return null;
        }
        
        $cacheTime = filemtime($this->cacheFile);
        if (time() - $cacheTime > $this->cacheLifetime) {
            return null;
        }
        
        $data = file_get_contents($this->cacheFile);
        return json_decode($data, true);
    }
    
    /**
     * Cache status
     */
    private function cacheStatus($status) {
        if (!Config::get('cache.enabled', true)) {
            return;
        }
        
        $cacheDir = dirname($this->cacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        file_put_contents($this->cacheFile, json_encode($status));
    }
    
    /**
     * Store status in database
     */
    private function storeStatusInDatabase($status) {
        try {
            $this->db->execute(
                "INSERT INTO server_status (
                    is_online, players_online, max_players, version, motd, ping_ms,
                    tps, memory_used_mb, memory_max_mb, cpu_usage, recorded_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $status['online'] ? 1 : 0,
                    $status['players']['online'],
                    $status['players']['max'],
                    $status['version'],
                    $status['motd'],
                    $status['ping'],
                    $status['performance']['tps'] ?? null,
                    $status['performance']['memory']['used'] ?? null,
                    $status['performance']['memory']['max'] ?? null,
                    $status['performance']['cpu'] ?? null,
                    date('Y-m-d H:i:s')
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to store server status: " . $e->getMessage());
        }
    }
    
    /**
     * Get performance metrics (placeholder)
     */
    private function getPerformanceMetrics() {
        // In a real implementation, this would connect to the server
        // via RCON or a plugin API to get performance data
        return [
            'tps' => 20.0,
            'memory' => [
                'used' => 2048 * 1024 * 1024, // 2GB in bytes
                'max' => 4096 * 1024 * 1024   // 4GB in bytes
            ],
            'cpu' => 15.5
        ];
    }
    
    /**
     * Get historical status data
     */
    public function getHistoricalData($hours = 24) {
        $since = date('Y-m-d H:i:s', time() - ($hours * 3600));
        
        return $this->db->fetchAll(
            "SELECT * FROM server_status WHERE recorded_at >= ? ORDER BY recorded_at ASC",
            [$since]
        );
    }
}

// Handle the request
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }
    
    $statusManager = new ServerStatusManager();
    
    // Check if historical data is requested
    if (isset($_GET['history'])) {
        $hours = min(168, max(1, intval($_GET['hours'] ?? 24))); // Max 1 week
        $data = $statusManager->getHistoricalData($hours);
        echo json_encode(['success' => true, 'data' => $data]);
    } else {
        // Get current status
        $status = $statusManager->getStatus();
        echo json_encode($status);
    }
    
} catch (Exception $e) {
    error_log("Server status API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'online' => false,
        'error' => 'Failed to retrieve server status',
        'timestamp' => time()
    ]);
}

?>
