// MineSea - Server Status JavaScript

class ServerStatusManager {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isUpdating = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }
    
    init() {
        // Start automatic updates
        this.startAutoUpdate();
        
        // Add visibility change listener to pause/resume updates
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoUpdate();
            } else {
                this.startAutoUpdate();
            }
        });
    }
    
    startAutoUpdate() {
        if (this.intervalId) return;
        
        // Initial update
        this.updateServerStatus();
        
        // Set up interval
        this.intervalId = setInterval(() => {
            this.updateServerStatus();
        }, this.updateInterval);
    }
    
    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    async updateServerStatus() {
        if (this.isUpdating) return;
        
        this.isUpdating = true;
        
        try {
            const response = await fetch('backend/api/server-status.php', {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.handleStatusUpdate(data);
            this.retryCount = 0; // Reset retry count on success
            
        } catch (error) {
            console.error('Error updating server status:', error);
            this.handleStatusError();
        } finally {
            this.isUpdating = false;
        }
    }
    
    handleStatusUpdate(data) {
        // Update main status display
        this.updateStatusDisplay(data);
        
        // Update detailed status if present
        this.updateDetailedStatus(data);
        
        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('serverStatusUpdate', {
            detail: data
        }));
    }
    
    updateStatusDisplay(data) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');
        const onlinePlayers = document.getElementById('online-players');
        const maxPlayers = document.getElementById('max-players');
        
        if (!statusDot || !statusText) return;
        
        if (data.online) {
            statusDot.style.backgroundColor = '#4caf50';
            statusDot.classList.remove('offline');
            statusDot.classList.add('online');
            statusText.textContent = 'Server Online';
            
            if (onlinePlayers && maxPlayers) {
                onlinePlayers.textContent = data.players?.online || 0;
                maxPlayers.textContent = data.players?.max || 0;
            }
        } else {
            statusDot.style.backgroundColor = '#f44336';
            statusDot.classList.remove('online');
            statusDot.classList.add('offline');
            statusText.textContent = 'Server Offline';
            
            if (onlinePlayers && maxPlayers) {
                onlinePlayers.textContent = '--';
                maxPlayers.textContent = '--';
            }
        }
        
        // Update last updated time
        this.updateLastUpdated();
    }
    
    updateDetailedStatus(data) {
        // Server version
        const versionElement = document.getElementById('server-version');
        if (versionElement && data.version) {
            versionElement.textContent = data.version;
        }
        
        // Server MOTD
        const motdElement = document.getElementById('server-motd');
        if (motdElement && data.motd) {
            motdElement.textContent = data.motd;
        }
        
        // Ping/Latency
        const pingElement = document.getElementById('server-ping');
        if (pingElement && data.ping) {
            pingElement.textContent = `${data.ping}ms`;
            
            // Add ping quality indicator
            pingElement.className = 'ping-value';
            if (data.ping < 50) {
                pingElement.classList.add('ping-excellent');
            } else if (data.ping < 100) {
                pingElement.classList.add('ping-good');
            } else if (data.ping < 200) {
                pingElement.classList.add('ping-fair');
            } else {
                pingElement.classList.add('ping-poor');
            }
        }
        
        // Player list
        this.updatePlayerList(data.players?.list || []);
        
        // Server performance metrics
        this.updatePerformanceMetrics(data.performance || {});
    }
    
    updatePlayerList(players) {
        const playerListElement = document.getElementById('online-players-list');
        if (!playerListElement) return;
        
        if (players.length === 0) {
            playerListElement.innerHTML = '<p class="no-players">No players online</p>';
            return;
        }
        
        const playerHTML = players.map(player => `
            <div class="player-item">
                <img src="https://mc-heads.net/avatar/${player.uuid}/24" 
                     alt="${player.name}" 
                     class="player-avatar"
                     onerror="this.src='assets/images/default-avatar.png'">
                <span class="player-name">${this.escapeHtml(player.name)}</span>
            </div>
        `).join('');
        
        playerListElement.innerHTML = playerHTML;
    }
    
    updatePerformanceMetrics(performance) {
        // TPS (Ticks Per Second)
        const tpsElement = document.getElementById('server-tps');
        if (tpsElement && performance.tps !== undefined) {
            tpsElement.textContent = performance.tps.toFixed(2);
            
            // Add TPS quality indicator
            tpsElement.className = 'tps-value';
            if (performance.tps >= 19.5) {
                tpsElement.classList.add('tps-excellent');
            } else if (performance.tps >= 18) {
                tpsElement.classList.add('tps-good');
            } else if (performance.tps >= 15) {
                tpsElement.classList.add('tps-fair');
            } else {
                tpsElement.classList.add('tps-poor');
            }
        }
        
        // Memory usage
        const memoryElement = document.getElementById('server-memory');
        if (memoryElement && performance.memory) {
            const usedGB = (performance.memory.used / 1024 / 1024 / 1024).toFixed(1);
            const maxGB = (performance.memory.max / 1024 / 1024 / 1024).toFixed(1);
            const percentage = ((performance.memory.used / performance.memory.max) * 100).toFixed(1);
            
            memoryElement.textContent = `${usedGB}GB / ${maxGB}GB (${percentage}%)`;
        }
        
        // CPU usage
        const cpuElement = document.getElementById('server-cpu');
        if (cpuElement && performance.cpu !== undefined) {
            cpuElement.textContent = `${performance.cpu.toFixed(1)}%`;
        }
    }
    
    updateLastUpdated() {
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            const now = new Date();
            lastUpdatedElement.textContent = `Last updated: ${now.toLocaleTimeString()}`;
        }
    }
    
    handleStatusError() {
        this.retryCount++;
        
        if (this.retryCount <= this.maxRetries) {
            console.log(`Retrying server status update (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => {
                this.updateServerStatus();
            }, 5000); // Retry after 5 seconds
            return;
        }
        
        // Max retries reached, show offline status
        const statusData = {
            online: false,
            players: { online: 0, max: 0 }
        };
        
        this.updateStatusDisplay(statusData);
        
        // Show error notification
        if (window.MineSea && window.MineSea.showNotification) {
            window.MineSea.showNotification(
                'Unable to connect to server. Status may be outdated.',
                'warning'
            );
        }
    }
    
    // Manual refresh function
    async forceUpdate() {
        this.retryCount = 0;
        await this.updateServerStatus();
    }
    
    // Utility function
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
}

// Initialize server status manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.serverStatusManager = new ServerStatusManager();
});

// Add refresh button functionality
document.addEventListener('DOMContentLoaded', function() {
    const refreshButton = document.getElementById('refresh-status');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Refreshing...';
            
            window.serverStatusManager.forceUpdate().finally(() => {
                this.disabled = false;
                this.textContent = 'Refresh';
            });
        });
    }
});

// Export for use in other scripts
window.ServerStatusManager = ServerStatusManager;
