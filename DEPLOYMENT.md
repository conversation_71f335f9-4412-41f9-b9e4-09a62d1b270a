# MineSea Deployment Guide

This guide provides instructions for deploying the MineSea website to a production environment.

## 🚀 Production Deployment

### Server Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: 7.4+ (8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **SSL Certificate**: Required for HTTPS
- **Memory**: Minimum 512MB RAM
- **Storage**: Minimum 1GB disk space

### Pre-Deployment Checklist

#### 1. Environment Configuration
- [ ] Update `backend/config/config.php` with production settings
- [ ] Set `error_reporting(0)` and `ini_set('display_errors', 0)`
- [ ] Configure proper database credentials
- [ ] Set up SMTP settings for email functionality
- [ ] Update site URLs and paths

#### 2. Security Configuration
- [ ] Generate secure random passwords
- [ ] Configure SSL/TLS certificates
- [ ] Set up firewall rules
- [ ] Enable HTTPS redirects
- [ ] Configure secure headers

#### 3. Database Setup
```sql
-- Create production database
CREATE DATABASE minesea_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database user
CREATE USER 'minesea_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON minesea_prod.* TO 'minesea_user'@'localhost';
FLUSH PRIVILEGES;

-- Import schema
mysql -u minesea_user -p minesea_prod < database/schema.sql
```

### Apache Configuration

#### Virtual Host Example
```apache
<VirtualHost *:80>
    ServerName minesea.net
    ServerAlias www.minesea.net
    DocumentRoot /var/www/minesea
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName minesea.net
    ServerAlias www.minesea.net
    DocumentRoot /var/www/minesea
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    SSLCertificateChainFile /path/to/chain.crt
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # PHP Configuration
    <FilesMatch \.php$>
        SetHandler "proxy:unix:/var/run/php/php8.0-fpm.sock|fcgi://localhost"
    </FilesMatch>
    
    # Asset Caching
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </LocationMatch>
    
    # Deny access to sensitive files
    <Files ~ "^\.">
        Order allow,deny
        Deny from all
    </Files>
    
    <Directory "/var/www/minesea/backend">
        Order allow,deny
        Deny from all
    </Directory>
    
    <Directory "/var/www/minesea/database">
        Order allow,deny
        Deny from all
    </Directory>
</VirtualHost>
```

### Nginx Configuration

#### Server Block Example
```nginx
server {
    listen 80;
    server_name minesea.net www.minesea.net;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name minesea.net www.minesea.net;
    root /var/www/minesea;
    index index.html index.php;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Asset Caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # PHP Processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Deny access to sensitive directories
    location ~ ^/(backend|database)/ {
        deny all;
        return 404;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        return 404;
    }
}
```

### File Permissions

```bash
# Set proper ownership
chown -R www-data:www-data /var/www/minesea

# Set directory permissions
find /var/www/minesea -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/minesea -type f -exec chmod 644 {} \;

# Set executable permissions for PHP files
chmod +x /var/www/minesea/backend/api/*.php

# Create writable directories
mkdir -p /var/www/minesea/{uploads,cache,logs}
chmod 755 /var/www/minesea/{uploads,cache,logs}
```

### Performance Optimization

#### 1. Enable Compression
```apache
# Apache
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</Location>
```

```nginx
# Nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

#### 2. Enable Browser Caching
```apache
# Apache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
</IfModule>
```

### Monitoring and Maintenance

#### 1. Log Monitoring
```bash
# Monitor error logs
tail -f /var/log/apache2/error.log
tail -f /var/log/nginx/error.log

# Monitor access logs
tail -f /var/log/apache2/access.log
tail -f /var/log/nginx/access.log

# Monitor PHP logs
tail -f /var/log/php8.0-fpm.log
```

#### 2. Database Maintenance
```sql
-- Regular maintenance queries
OPTIMIZE TABLE users;
OPTIMIZE TABLE player_statistics;
OPTIMIZE TABLE server_status;

-- Clean old server status records (keep last 30 days)
DELETE FROM server_status WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean expired sessions
DELETE FROM user_sessions WHERE expires_at < NOW();
```

#### 3. Backup Strategy
```bash
#!/bin/bash
# Daily backup script

# Database backup
mysqldump -u minesea_user -p minesea_prod > /backups/minesea_$(date +%Y%m%d).sql

# File backup
tar -czf /backups/minesea_files_$(date +%Y%m%d).tar.gz /var/www/minesea

# Keep only last 7 days of backups
find /backups -name "minesea_*" -mtime +7 -delete
```

### SSL Certificate Setup (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache

# Obtain certificate
sudo certbot --apache -d minesea.net -d www.minesea.net

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Post-Deployment Testing

#### 1. Functionality Tests
- [ ] Homepage loads correctly
- [ ] User registration works
- [ ] User login works
- [ ] Server status updates
- [ ] News system functions
- [ ] Mobile responsiveness
- [ ] Form submissions work

#### 2. Performance Tests
- [ ] Page load times < 3 seconds
- [ ] Images optimized and loading
- [ ] CSS/JS minified and compressed
- [ ] Database queries optimized

#### 3. Security Tests
- [ ] HTTPS enforced
- [ ] Security headers present
- [ ] Sensitive files protected
- [ ] SQL injection prevention
- [ ] XSS protection enabled

### Troubleshooting

#### Common Issues
1. **Database Connection Failed**
   - Check credentials in config.php
   - Verify MySQL service is running
   - Check firewall settings

2. **PHP Errors**
   - Check PHP error logs
   - Verify PHP extensions installed
   - Check file permissions

3. **CSS/JS Not Loading**
   - Check file paths
   - Verify web server configuration
   - Check browser console for errors

#### Support Resources
- Server logs: `/var/log/`
- PHP logs: `/var/log/php/`
- Application logs: `/var/www/minesea/logs/`

---

**Deployment Complete!** 🚀

Your MineSea website should now be live and accessible to users.
