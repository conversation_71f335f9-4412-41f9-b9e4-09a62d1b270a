/* MineSea - Main CSS */

/* CSS Variables for Ocean Theme */
:root {
    --primary-blue: #0066cc;
    --secondary-blue: #004499;
    --accent-teal: #00a6a6;
    --light-blue: #e6f3ff;
    --dark-blue: #003366;
    --ocean-gradient: linear-gradient(135deg, #0066cc 0%, #00a6a6 100%);
    --wave-gradient: linear-gradient(45deg, rgba(0, 102, 204, 0.1) 0%, rgba(0, 166, 166, 0.1) 100%);
    
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    --background-light: #ffffff;
    --background-gray: #f8f9fa;
    --border-color: #e0e0e0;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 12px;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-light);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary-blue);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--ocean-gradient);
    color: var(--text-light);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--text-light);
}

.btn-discord {
    background: #5865f2;
    color: var(--text-light);
}

.btn-discord:hover {
    background: #4752c4;
}

.btn-full {
    width: 100%;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-medium);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-decoration: none;
}

.nav-logo a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: inherit;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--ocean-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: var(--shadow-light);
    flex-shrink: 0;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.logo-img::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--transition-medium);
}

.nav-logo:hover .logo-img {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.nav-logo:hover .logo-img::before {
    animation: shimmer 0.6s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text {
    background: var(--ocean-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--ocean-gradient);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.login-btn {
    background: var(--ocean-gradient);
    color: var(--text-light) !important;
    padding: 8px 16px;
    border-radius: var(--border-radius);
}

.login-btn::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: var(--ocean-gradient);
    color: var(--text-light);
    text-align: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23ffffff"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23ffffff"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23ffffff"></path></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: wave 10s linear infinite;
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1200px; }
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.highlight {
    background: linear-gradient(45deg, #00a6a6, #66d9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.server-status {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1rem;
    display: inline-block;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.player-count {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: var(--background-gray);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--background-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.about-image img {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

.server-preview-placeholder {
    width: 100%;
    min-height: 300px;
    background: var(--ocean-gradient);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-align: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.server-preview-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".1" fill="%23ffffff"></path></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: wave 10s linear infinite;
    pointer-events: none;
}

.preview-content {
    position: relative;
    z-index: 2;
}

.preview-content h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.preview-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.preview-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.preview-stats span {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Avatar Placeholder */
.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--ocean-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
}

.avatar-placeholder:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-heavy);
}

.avatar-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Features Section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: var(--transition-medium);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

/* Community Section */
.community {
    background: var(--background-gray);
}

.community-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.news-section, .discord-section {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.news-item {
    padding: 1rem;
    border-left: 4px solid var(--primary-blue);
    background: var(--background-gray);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.news-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.news-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    padding: 1.5rem;
    background: var(--background-gray);
    border-radius: var(--border-radius);
}

.contact-item h4 {
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group textarea {
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
}

/* Footer */
.footer {
    background: var(--dark-blue);
    color: var(--text-light);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--accent-teal);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--accent-teal);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Animation Classes */
.animate-in {
    animation: fadeInUp 0.6s ease-out;
}

.animate-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-scale {
    animation: scaleIn 0.4s ease-out;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Loading Animations */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-weight: 500;
    z-index: 10000;
    transform: translateX(400px);
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.notification-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.notification-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.notification-info {
    background: var(--ocean-gradient);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-blue);
}

/* Selection Styling */
::selection {
    background: var(--primary-blue);
    color: var(--text-light);
}

::-moz-selection {
    background: var(--primary-blue);
    color: var(--text-light);
}

/* Focus Styles */
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Hover Effects */
.feature-card:hover .feature-icon {
    transform: scale(1.1);
    transition: var(--transition-medium);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.news-item:hover {
    transform: translateX(5px);
    transition: var(--transition-fast);
}

/* Button Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Navbar Scroll Effect */
.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-light);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.pointer { cursor: pointer; }
.no-select { user-select: none; }

/* Dashboard Styles */
.dashboard-header {
    background: var(--ocean-gradient);
    color: var(--text-light);
    padding: 6rem 0 3rem;
    margin-top: 70px;
}

.welcome-banner {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-banner h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.welcome-banner p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.quick-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.dashboard-content {
    padding: 3rem 0;
    background: var(--background-gray);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition-medium);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.dashboard-card h3 {
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* Server Status Widget */
.server-status-widget {
    text-align: center;
}

.server-status-widget .status-indicator {
    margin-bottom: 1rem;
}

.server-status-widget .player-count {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.server-info p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Stats Widget */
.stats-widget {
    margin-bottom: 1.5rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--primary-blue);
}

/* Activity Widget */
.activity-widget {
    margin-bottom: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* News Widget */
.news-widget {
    margin-bottom: 1.5rem;
}

.news-item-small {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.news-item-small:last-child {
    border-bottom: none;
}

.news-item-small .news-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.news-item-small .news-excerpt {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.news-item-small .news-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Quick Links */
.quick-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--background-gray);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-fast);
}

.quick-link:hover {
    background: var(--primary-blue);
    color: var(--text-light);
    transform: translateY(-1px);
}

.link-icon {
    font-size: 1.1rem;
}

.link-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Friends Widget */
.friends-widget {
    margin-bottom: 1.5rem;
}

.friend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.friend-item:last-child {
    border-bottom: none;
}

.friend-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.friend-name {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.friend-status {
    font-size: 0.8rem;
}

.friend-status.online {
    color: #4caf50;
}

.friend-status.offline {
    color: var(--text-secondary);
}

/* Empty States */
.no-friends,
.no-news,
.no-activity {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem 0;
}
