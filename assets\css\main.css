/* MineSea - Enhanced Premium Design System */

/* CSS Variables - Sophisticated Ocean Theme with Dark Mode Support */
:root {
    /* Enhanced Ocean Theme Colors - Light Mode */
    --primary-ocean-50: #eff6ff;
    --primary-ocean-100: #dbeafe;
    --primary-ocean-200: #bfdbfe;
    --primary-ocean-300: #93c5fd;
    --primary-ocean-400: #60a5fa;
    --primary-ocean-500: #3b82f6;
    --primary-ocean-600: #2563eb;
    --primary-ocean-700: #1d4ed8;
    --primary-ocean-800: #1e40af;
    --primary-ocean-900: #1e3a8a;
    --primary-ocean-950: #172554;

    --secondary-teal-50: #f0fdfa;
    --secondary-teal-100: #ccfbf1;
    --secondary-teal-200: #99f6e4;
    --secondary-teal-300: #5eead4;
    --secondary-teal-400: #2dd4bf;
    --secondary-teal-500: #14b8a6;
    --secondary-teal-600: #0d9488;
    --secondary-teal-700: #0f766e;
    --secondary-teal-800: #115e59;
    --secondary-teal-900: #134e4a;
    --secondary-teal-950: #042f2e;

    --accent-coral-50: #fef2f2;
    --accent-coral-100: #fee2e2;
    --accent-coral-200: #fecaca;
    --accent-coral-300: #fca5a5;
    --accent-coral-400: #f87171;
    --accent-coral-500: #ef4444;
    --accent-coral-600: #dc2626;
    --accent-coral-700: #b91c1c;
    --accent-coral-800: #991b1b;
    --accent-coral-900: #7f1d1d;
    --accent-coral-950: #450a0a;

    /* Enhanced Neutral Colors - Light Mode */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --neutral-950: #0a0a0a;

    /* Semantic Light Mode Colors */
    --background-primary: var(--neutral-50);
    --background-secondary: #ffffff;
    --background-tertiary: var(--neutral-100);
    --background-elevated: #ffffff;

    --text-primary: var(--neutral-900);
    --text-secondary: var(--neutral-700);
    --text-tertiary: var(--neutral-500);
    --text-inverse: #ffffff;

    --border-primary: var(--neutral-200);
    --border-secondary: var(--neutral-300);
    --border-focus: var(--primary-ocean-500);

    /* Surface Colors */
    --surface-primary: #ffffff;
    --surface-secondary: var(--neutral-50);
    --surface-tertiary: var(--neutral-100);
    --surface-overlay: rgba(255, 255, 255, 0.95);
}

/* Dark Mode Color Variables */
[data-theme="dark"] {
    /* Enhanced Ocean Theme Colors - Dark Mode */
    --primary-ocean-50: #172554;
    --primary-ocean-100: #1e3a8a;
    --primary-ocean-200: #1e40af;
    --primary-ocean-300: #1d4ed8;
    --primary-ocean-400: #2563eb;
    --primary-ocean-500: #3b82f6;
    --primary-ocean-600: #60a5fa;
    --primary-ocean-700: #93c5fd;
    --primary-ocean-800: #bfdbfe;
    --primary-ocean-900: #dbeafe;
    --primary-ocean-950: #eff6ff;

    --secondary-teal-50: #042f2e;
    --secondary-teal-100: #134e4a;
    --secondary-teal-200: #115e59;
    --secondary-teal-300: #0f766e;
    --secondary-teal-400: #0d9488;
    --secondary-teal-500: #14b8a6;
    --secondary-teal-600: #2dd4bf;
    --secondary-teal-700: #5eead4;
    --secondary-teal-800: #99f6e4;
    --secondary-teal-900: #ccfbf1;
    --secondary-teal-950: #f0fdfa;

    /* Dark Mode Semantic Colors */
    --background-primary: #0f1419;
    --background-secondary: #1a202c;
    --background-tertiary: #2d3748;
    --background-elevated: #1a202c;

    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #a0aec0;
    --text-inverse: var(--neutral-900);

    --border-primary: #4a5568;
    --border-secondary: #2d3748;
    --border-focus: var(--primary-ocean-400);

    /* Dark Surface Colors */
    --surface-primary: #1a202c;
    --surface-secondary: #2d3748;
    --surface-tertiary: #4a5568;
    --surface-overlay: rgba(26, 32, 44, 0.95);

    /* Dark Mode Adjustments */
    --neutral-50: var(--neutral-950);
    --neutral-100: var(--neutral-900);
    --neutral-200: var(--neutral-800);
    --neutral-300: var(--neutral-700);
    --neutral-400: var(--neutral-600);
    --neutral-500: var(--neutral-500);
    --neutral-600: var(--neutral-400);
    --neutral-700: var(--neutral-300);
    --neutral-800: var(--neutral-200);
    --neutral-900: var(--neutral-100);
    --neutral-950: var(--neutral-50);

    /* Functional Colors */
    --success-color: #10b981;
    --success-light: #d1fae5;
    --warning-color: #f59e0b;
    --warning-light: #fef3c7;
    --danger-color: #ef4444;
    --danger-light: #fee2e2;
    --info-color: #3b82f6;
    --info-light: #dbeafe;

    /* Enhanced 8px Grid Spacing System */
    --space-0: 0;
    --space-1: 0.125rem;    /* 2px */
    --space-2: 0.25rem;     /* 4px */
    --space-3: 0.375rem;    /* 6px */
    --space-4: 0.5rem;      /* 8px */
    --space-5: 0.625rem;    /* 10px */
    --space-6: 0.75rem;     /* 12px */
    --space-8: 1rem;        /* 16px */
    --space-10: 1.25rem;    /* 20px */
    --space-12: 1.5rem;     /* 24px */
    --space-16: 2rem;       /* 32px */
    --space-20: 2.5rem;     /* 40px */
    --space-24: 3rem;       /* 48px */
    --space-32: 4rem;       /* 64px */
    --space-40: 5rem;       /* 80px */
    --space-48: 6rem;       /* 96px */
    --space-56: 7rem;       /* 112px */
    --space-64: 8rem;       /* 128px */
    --space-80: 10rem;      /* 160px */
    --space-96: 12rem;      /* 192px */

    /* Enhanced Gradients */
    --gradient-ocean: linear-gradient(135deg, var(--primary-ocean-600) 0%, var(--secondary-teal-500) 50%, var(--primary-ocean-500) 100%);
    --gradient-ocean-subtle: linear-gradient(135deg, var(--primary-ocean-50) 0%, var(--secondary-teal-50) 100%);
    --gradient-hero: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--background-tertiary) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    --gradient-glass-dark: linear-gradient(135deg, rgba(26, 32, 44, 0.8) 0%, rgba(26, 32, 44, 0.6) 100%);
    --gradient-card: linear-gradient(135deg, var(--surface-primary) 0%, var(--surface-secondary) 100%);

    /* Enhanced Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    --shadow-glow: 0 0 0 1px rgba(59, 130, 246, 0.5);
    --shadow-glow-lg: 0 0 0 1px rgba(59, 130, 246, 0.5), 0 4px 16px rgba(59, 130, 246, 0.3);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    --shadow-floating: 0 12px 24px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);

    /* Enhanced Typography Scale */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    --font-size-3xl: 1.875rem;    /* 30px */
    --font-size-4xl: 2.25rem;     /* 36px */
    --font-size-5xl: 3rem;        /* 48px */
    --font-size-6xl: 3.75rem;     /* 60px */
    --font-size-7xl: 4.5rem;      /* 72px */
    --font-size-8xl: 6rem;        /* 96px */
    --font-size-9xl: 8rem;        /* 128px */

    /* Enhanced Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Letter Spacing */
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;

    /* Border Radius */
    --border-radius-xs: 0.125rem;
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --border-radius-2xl: 2rem;
    --border-radius-full: 9999px;

    /* Transitions - Optimized for Performance */
    --transition: all 0.2s ease;
    --transition-slow: all 0.3s ease;
    --transition-fast: all 0.15s ease;

    /* Blur Effects */
    --blur-sm: blur(4px);
    --blur-md: blur(8px);
    --blur-lg: blur(16px);

    /* Z-index Layers */
    --z-negative: -1;
    --z-normal: 1;
    --z-tooltip: 10;
    --z-fixed: 100;
    --z-modal: 1000;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
}

/* Modern Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background: var(--background-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modern Container System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.container-2xl {
    max-width: 1536px;
}

/* Modern Section Spacing */
.section {
    padding: 5rem 0;
}

.section-sm {
    padding: 3rem 0;
}

.section-lg {
    padding: 7rem 0;
}

.section-xl {
    padding: 10rem 0;
}

/* Modern Typography System */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--dark-navy);
    letter-spacing: -0.025em;
}

.heading-1,
h1 {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.05em;
}

.heading-2,
h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: 1.15;
    letter-spacing: -0.025em;
}

.heading-3,
h3 {
    font-size: var(--font-size-3xl);
    font-weight: 600;
    line-height: 1.2;
}

.heading-4,
h4 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    line-height: 1.25;
}

.heading-5,
h5 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    line-height: 1.3;
}

.heading-6,
h6 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    line-height: 1.35;
}

/* Text Styles */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

/* Text Colors */
.text-primary { color: var(--dark-navy); }
.text-secondary { color: var(--gray-color); }
.text-muted { color: var(--light-color-400); }
.text-white { color: white; }
.text-ocean { color: var(--primary-ocean); }
.text-teal { color: var(--secondary-teal); }

/* Text Weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* Paragraph Styles */
p {
    margin-bottom: 1rem;
    color: var(--gray-color);
    line-height: 1.7;
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: 400;
    line-height: 1.6;
    color: var(--dark-navy-600);
}

/* Link Styles */
a {
    color: var(--primary-ocean);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-ocean-dark);
}

.link-underline {
    text-decoration: underline;
    text-underline-offset: 0.25rem;
    text-decoration-thickness: 2px;
    text-decoration-color: var(--primary-ocean-light);
}

.link-underline:hover {
    text-decoration-color: var(--primary-ocean);
}

/* Enhanced Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    padding: var(--space-6) var(--space-12);
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    outline: none;
    min-height: 2.75rem;
    letter-spacing: var(--tracking-wide);
}

.btn:focus {
    outline: none;
    box-shadow: var(--shadow-glow-lg);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
}

/* Loading State */
.btn.loading {
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: btn-spin 1s linear infinite;
    color: white;
}

@keyframes btn-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button with Icon */
.btn-icon {
    font-size: 1.1em;
    line-height: 1;
}

/* Micro-interactions */
.btn:not(:disabled):active {
    transform: scale(0.98);
}

/* Button Sizes */
.btn-xs {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-xs);
    min-height: 2rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
    min-height: 2.25rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
    min-height: 3.5rem;
}

.btn-xl {
    padding: 1.25rem 2.5rem;
    font-size: var(--font-size-xl);
    min-height: 4rem;
}

/* Enhanced Button Variants */
.btn-primary {
    background: var(--gradient-ocean);
    color: white;
    box-shadow: var(--shadow-md);
    border: 1px solid transparent;
    position: relative;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-floating);
}

.btn-primary:hover::before {
    opacity: 1;
}

.btn-primary:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: white;
    color: var(--primary-ocean);
    border: 2px solid var(--primary-ocean);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--primary-ocean);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary-ocean);
    border: 2px solid var(--primary-ocean);
}

.btn-outline:hover {
    background: var(--primary-ocean);
    color: white;
}

.btn-ghost {
    background: transparent;
    color: var(--primary-ocean);
    border: none;
}

.btn-ghost:hover {
    background: var(--primary-ocean-ultra-light);
}

.btn-glass {
    background: var(--gradient-glass);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--blur-md);
    box-shadow: var(--shadow-glass);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-warning:hover {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Button Modifiers */
.btn-full {
    width: 100%;
}

.btn-rounded {
    border-radius: var(--border-radius-full);
}

.btn-square {
    border-radius: 0;
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border-right: none;
}

/* Enhanced Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--surface-overlay);
    backdrop-filter: var(--blur-md);
    border-bottom: 1px solid var(--border-primary);
    z-index: var(--z-fixed);
    transition: all 0.3s ease;
    height: 4rem;
}

.navbar.scrolled {
    background: var(--surface-overlay);
    box-shadow: var(--shadow-elevated);
    border-bottom-color: var(--border-secondary);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--space-16);
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: var(--space-2);
    border-radius: var(--border-radius);
}

.nav-logo:hover {
    transform: translateY(-1px);
    background: var(--surface-secondary);
}

.logo-img {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius);
    background: var(--gradient-ocean);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.logo-img::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(45deg) translateX(-100%);
    transition: var(--transition-slow);
}

.nav-logo:hover .logo-img {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.nav-logo:hover .logo-img::before {
    transform: rotate(45deg) translateX(100%);
}

.logo-text {
    background: var(--gradient-ocean);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: var(--font-size-xl);
    letter-spacing: var(--tracking-tight);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-16);
    align-items: center;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: var(--font-size-base);
    text-decoration: none;
    padding: var(--space-4) var(--space-6);
    transition: all 0.3s ease;
    position: relative;
    display: block;
    border-radius: var(--border-radius);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-ocean-600);
    background: var(--surface-secondary);
    transform: translateY(-1px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-ocean);
    border-radius: 1px;
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.login-btn {
    background: var(--gradient-ocean) !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius) !important;
    font-weight: 600 !important;
    box-shadow: var(--shadow-sm) !important;
    border: none !important;
}

.login-btn::after {
    display: none !important;
}

.login-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
    color: white !important;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 0.25rem;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.hamburger:hover {
    background: var(--light-color-200);
}

.bar {
    width: 1.5rem;
    height: 2px;
    background: var(--dark-navy);
    transition: var(--transition);
    border-radius: 1px;
}

.hamburger.active .bar:nth-child(1) {
    transform: translateY(6px) rotate(45deg);
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
}

/* Dark Mode Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: var(--border-radius);
    background: var(--surface-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.theme-toggle:hover {
    background: var(--surface-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.theme-toggle:focus {
    outline: none;
    box-shadow: var(--shadow-glow);
}

.theme-toggle-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.sun-icon,
.moon-icon {
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.3s ease;
}

.sun-icon {
    opacity: 1;
    transform: rotate(0deg) scale(1);
}

.moon-icon {
    opacity: 0;
    transform: rotate(180deg) scale(0.8);
}

[data-theme="dark"] .sun-icon {
    opacity: 0;
    transform: rotate(-180deg) scale(0.8);
}

[data-theme="dark"] .moon-icon {
    opacity: 1;
    transform: rotate(0deg) scale(1);
}

/* Dark mode transition for all elements */
* {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Enhanced Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 4rem;
    padding-bottom: var(--space-32);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-negative);
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-hero);
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 102, 204, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 166, 166, 0.05) 0%, transparent 50%);
    background-size: 200px 200px;
    /* Removed animation for better performance */
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-32);
    align-items: center;
    width: 100%;
    position: relative;
    z-index: var(--z-normal);
}

.hero-main {
    color: var(--text-inverse);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4) var(--space-8);
    background: var(--gradient-glass);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-full);
    backdrop-filter: var(--blur-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--space-16);
    box-shadow: var(--shadow-glass);
    transition: all 0.3s ease;
}

.hero-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-floating);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.05em;
}

.hero-highlight {
    background: var(--gradient-ocean);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-description {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    max-width: 90%;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.stat-card {
    text-align: center;
    padding: 1rem;
    background: var(--gradient-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: var(--blur-md);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* Hero Visual - Server Preview */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.server-preview {
    width: 100%;
    max-width: 400px;
}

.preview-window {
    background: var(--gradient-glass-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    backdrop-filter: var(--blur-lg);
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.window-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.window-controls {
    display: flex;
    gap: 0.5rem;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control-dot.red {
    background: #ff5f57;
}

.control-dot.yellow {
    background: #ffbd2e;
}

.control-dot.green {
    background: #28ca42;
}

.window-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.window-content {
    padding: 2rem 1.5rem;
}

.server-info {
    color: white;
}

.server-status-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

.status-text {
    font-weight: 600;
    color: var(--success-color);
}

.server-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.detail-value {
    font-size: var(--font-size-sm);
    color: white;
    font-weight: 600;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23ffffff"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23ffffff"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23ffffff"></path></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: wave 10s linear infinite;
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1200px; }
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.highlight {
    background: linear-gradient(45deg, #00a6a6, #66d9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.server-status {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1rem;
    display: inline-block;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.player-count {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Modern Server Information Section */
.server-info {
    background: white;
    position: relative;
}

.server-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(0, 102, 204, 0.02) 0%,
        rgba(0, 166, 166, 0.02) 50%,
        rgba(51, 153, 255, 0.02) 100%);
    pointer-events: none;
}

.server-info-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
    position: relative;
    z-index: var(--z-normal);
}

.info-main {
    padding-right: 2rem;
}

.info-highlights {
    margin: 2.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.highlight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--light-color-200);
    transition: var(--transition);
}

.highlight-item:hover {
    transform: translateX(8px);
    border-color: var(--primary-ocean-light);
    box-shadow: var(--shadow-md);
}

.highlight-icon {
    width: 3rem;
    height: 3rem;
    background: var(--gradient-ocean-light);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
    border: 1px solid var(--primary-ocean-light);
}

.highlight-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-navy);
    margin-bottom: 0.5rem;
}

.highlight-content p {
    color: var(--gray-color);
    margin-bottom: 0;
    line-height: 1.5;
}

.server-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Stats Container */
.info-stats {
    position: sticky;
    top: 6rem;
}

.stats-container {
    background: white;
    border: 1px solid var(--light-color-200);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.stats-header {
    text-align: center;
    margin-bottom: 2rem;
}

.stats-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--dark-navy);
    margin-bottom: 0.5rem;
}

.stats-header p {
    color: var(--gray-color);
    margin-bottom: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card-large {
    background: var(--light-color);
    border: 1px solid var(--light-color-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
}

.stat-card-large:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-ocean-light);
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-ocean);
    margin-bottom: 0.25rem;
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--dark-navy);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.stat-trend {
    font-size: var(--font-size-xs);
    color: var(--success-color);
    font-weight: 500;
}

/* Server Specifications */
.server-specs {
    border-top: 1px solid var(--light-color-200);
    padding-top: 1.5rem;
}

.server-specs h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-navy);
    margin-bottom: 1rem;
    text-align: center;
}

.specs-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--light-color-200);
}

.spec-label {
    font-size: var(--font-size-sm);
    color: var(--gray-color);
    font-weight: 500;
}

.spec-value {
    font-size: var(--font-size-sm);
    color: var(--dark-navy);
    font-weight: 600;
}

.about-image img {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

.server-preview-placeholder {
    width: 100%;
    min-height: 300px;
    background: var(--ocean-gradient);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-align: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.server-preview-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".1" fill="%23ffffff"></path></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: wave 10s linear infinite;
    pointer-events: none;
}

.preview-content {
    position: relative;
    z-index: 2;
}

.preview-content h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.preview-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.preview-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.preview-stats span {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Avatar Placeholder */
.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--ocean-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
}

.avatar-placeholder:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-heavy);
}

.avatar-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Modern Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--gradient-ocean-light);
    border: 1px solid var(--primary-ocean-light);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-ocean);
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--dark-navy);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--gray-color);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Modern Features Section */
.features {
    background: var(--light-color);
    position: relative;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(0, 102, 204, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 166, 166, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: var(--z-normal);
}

.feature-card {
    background: var(--surface-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-xl);
    padding: var(--space-16);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-ocean);
    transform: scaleX(0);
    transition: var(--transition);
    transform-origin: left;
}

.feature-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-floating);
    border-color: var(--primary-ocean-300);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.feature-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: var(--gradient-ocean-light);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--primary-ocean-light);
}

.icon-emoji {
    font-size: 1.5rem;
}

.feature-badge {
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-xs);
    font-weight: 600;
    border-radius: var(--border-radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-card[data-feature="islands"] .feature-badge {
    background: var(--warning-light);
    color: var(--warning-color);
}

.feature-card[data-feature="towns"] .feature-badge {
    background: var(--info-light);
    color: var(--info-color);
}

.feature-card[data-feature="fishing"] .feature-badge {
    background: var(--success-light);
    color: var(--success-color);
}

.feature-card[data-feature="protection"] .feature-badge {
    background: var(--danger-light);
    color: var(--danger-color);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--dark-navy);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-description {
    color: var(--gray-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-sm);
    color: var(--dark-navy-600);
    font-weight: 500;
}

.feature-list li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: 700;
    font-size: 0.875rem;
}

/* Community Section */
.community {
    background: var(--background-gray);
}

.community-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.news-section, .discord-section {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.news-item {
    padding: 1rem;
    border-left: 4px solid var(--primary-blue);
    background: var(--background-gray);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.news-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.news-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    padding: 1.5rem;
    background: var(--background-gray);
    border-radius: var(--border-radius);
}

.contact-item h4 {
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group textarea {
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
}

/* Footer */
.footer {
    background: var(--dark-blue);
    color: var(--text-light);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--accent-teal);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--accent-teal);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Optimized Animations - Reduced for Performance */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* Enhanced Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero-pattern {
        animation: none !important;
    }

    .status-dot {
        animation: none !important;
    }

    .btn:hover {
        transform: none !important;
    }

    .feature-card:hover {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #000000;
        --border-secondary: #000000;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.5);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
    }

    .btn {
        border: 2px solid currentColor;
    }

    .feature-card {
        border: 2px solid var(--border-primary);
    }
}

/* Focus visible for better keyboard navigation */
.btn:focus-visible,
.nav-link:focus-visible,
.theme-toggle:focus-visible {
    outline: 2px solid var(--primary-ocean-500);
    outline-offset: 2px;
    box-shadow: var(--shadow-glow-lg);
}

/* Skip to content link for accessibility */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-ocean-600);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s ease;
}

.skip-to-content:focus {
    top: 6px;
}

/* Loading Skeletons */
.skeleton {
    background: linear-gradient(90deg, var(--surface-secondary) 25%, var(--surface-tertiary) 50%, var(--surface-secondary) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--space-2);
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    border-radius: var(--border-radius-lg);
}

/* Error States */
.error-state {
    text-align: center;
    padding: var(--space-24);
    color: var(--text-secondary);
}

.error-state-icon {
    font-size: 3rem;
    margin-bottom: var(--space-8);
    opacity: 0.5;
}

.error-state-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.error-state-description {
    margin-bottom: var(--space-8);
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--space-32);
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: var(--space-12);
    opacity: 0.3;
}

.empty-state-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

.empty-state-description {
    margin-bottom: var(--space-12);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Optimized Animation Classes */
.float-animation {
    animation: float 6s ease-in-out infinite;
}

/* Intersection Observer classes for performance */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animations */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-weight: 500;
    z-index: 10000;
    transform: translateX(400px);
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.notification-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.notification-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.notification-info {
    background: var(--ocean-gradient);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-blue);
}

/* Selection Styling */
::selection {
    background: var(--primary-blue);
    color: var(--text-light);
}

::-moz-selection {
    background: var(--primary-blue);
    color: var(--text-light);
}

/* Focus Styles */
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Hover Effects */
.feature-card:hover .feature-icon {
    transform: scale(1.1);
    transition: var(--transition-medium);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.news-item:hover {
    transform: translateX(5px);
    transition: var(--transition-fast);
}

/* Button Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Navbar Scroll Effect */
.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-light);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.pointer { cursor: pointer; }
.no-select { user-select: none; }

/* Dashboard Styles */
.dashboard-header {
    background: var(--ocean-gradient);
    color: var(--text-light);
    padding: 6rem 0 3rem;
    margin-top: 70px;
}

.welcome-banner {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-banner h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.welcome-banner p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.quick-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.dashboard-content {
    padding: 3rem 0;
    background: var(--background-gray);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition-medium);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.dashboard-card h3 {
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* Server Status Widget */
.server-status-widget {
    text-align: center;
}

.server-status-widget .status-indicator {
    margin-bottom: 1rem;
}

.server-status-widget .player-count {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.server-info p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Stats Widget */
.stats-widget {
    margin-bottom: 1.5rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--primary-blue);
}

/* Activity Widget */
.activity-widget {
    margin-bottom: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* News Widget */
.news-widget {
    margin-bottom: 1.5rem;
}

.news-item-small {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.news-item-small:last-child {
    border-bottom: none;
}

.news-item-small .news-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.news-item-small .news-excerpt {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.news-item-small .news-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Quick Links */
.quick-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--background-gray);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-fast);
}

.quick-link:hover {
    background: var(--primary-blue);
    color: var(--text-light);
    transform: translateY(-1px);
}

.link-icon {
    font-size: 1.1rem;
}

.link-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Friends Widget */
.friends-widget {
    margin-bottom: 1.5rem;
}

.friend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.friend-item:last-child {
    border-bottom: none;
}

.friend-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.friend-name {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.friend-status {
    font-size: 0.8rem;
}

.friend-status.online {
    color: #4caf50;
}

.friend-status.offline {
    color: var(--text-secondary);
}

/* Empty States */
.no-friends,
.no-news,
.no-activity {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem 0;
}
