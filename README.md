# MineSea - Minecraft Community Website

A modern, professional website for the MineSea Minecraft community server featuring an ocean/sea theme, user authentication, real-time server status, and comprehensive community features.

## 🌊 Features

### Frontend
- **Modern Design**: Ultra-modern, clean aesthetic with ocean/sea theme
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile devices
- **Smooth Animations**: CSS3 animations and transitions for enhanced user experience
- **Interactive Navigation**: Smooth scrolling navigation with mobile hamburger menu
- **Real-time Updates**: Live server status and dynamic content loading

### Backend
- **User Authentication**: Complete login/register system with session management
- **Database Integration**: MySQL database with comprehensive schema
- **API Endpoints**: RESTful APIs for server status, news, user management
- **Security Features**: Password hashing, CSRF protection, rate limiting
- **Server Integration**: Real-time Minecraft server status checking

### User Features
- **Player Profiles**: Detailed player statistics and achievements
- **Dashboard**: Personalized dashboard with quick access to key information
- **News System**: Latest announcements and community updates
- **Friends System**: Add and manage friends with online status
- **Server Status**: Real-time server information and player count

## 🚀 Installation

### Prerequisites
- Web server (Apache/Nginx)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Modern web browser

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd MineSea
   ```

2. **Database Setup**
   - Create a MySQL database named `minesea_db`
   - Import the schema from `database/schema.sql`
   - Update database credentials in `backend/config/database.php`

3. **Configuration**
   - Update settings in `backend/config/config.php`
   - Configure SMTP settings for email functionality
   - Set up Minecraft server connection details

4. **File Permissions**
   - Ensure write permissions for `uploads/`, `cache/`, and `logs/` directories
   - Set appropriate permissions for PHP files

5. **Web Server Configuration**
   - Point document root to the project directory
   - Ensure PHP is properly configured
   - Enable URL rewriting if needed

## 📁 Project Structure

```
MineSea/
├── assets/
│   ├── css/
│   │   ├── main.css          # Main stylesheet
│   │   ├── responsive.css    # Responsive design
│   │   └── auth.css          # Authentication pages
│   ├── js/
│   │   ├── main.js           # Core JavaScript
│   │   ├── auth.js           # Authentication logic
│   │   ├── server-status.js  # Server status functionality
│   │   └── dashboard.js      # Dashboard functionality
│   └── images/               # Image assets
├── backend/
│   ├── api/                  # API endpoints
│   ├── config/               # Configuration files
│   └── includes/             # PHP includes
├── database/
│   └── schema.sql            # Database schema
├── index.html                # Homepage
├── login.html                # Login page
├── register.html             # Registration page
├── dashboard.html            # User dashboard
├── profile.html              # User profile
└── README.md                 # This file
```

## 🎨 Design Features

### Color Scheme
- **Primary Blue**: #0066cc
- **Secondary Blue**: #004499
- **Accent Teal**: #00a6a6
- **Ocean Gradient**: Linear gradient from blue to teal

### Typography
- **Font Family**: Inter (Google Fonts)
- **Responsive Text**: Scales appropriately across devices
- **Accessibility**: High contrast ratios for readability

### Animations
- **Smooth Transitions**: 0.3s ease transitions
- **Hover Effects**: Interactive elements with visual feedback
- **Loading States**: Animated loading indicators
- **Wave Animation**: CSS-only ocean wave effects

## 🔧 API Endpoints

### Authentication
- `POST /backend/api/login.php` - User login
- `POST /backend/api/register.php` - User registration
- `GET /backend/api/auth-check.php` - Check authentication status
- `POST /backend/api/logout.php` - User logout

### Server Status
- `GET /backend/api/server-status.php` - Current server status
- `GET /backend/api/server-status.php?history=true` - Historical data

### Content
- `GET /backend/api/news.php` - Latest news and announcements
- `GET /backend/api/player-stats.php` - Player statistics
- `POST /backend/api/contact.php` - Contact form submission

## 📱 Responsive Design

The website is fully responsive and optimized for:
- **Desktop**: 1200px+ (Full layout with all features)
- **Tablet**: 768px-1199px (Adapted layout with touch-friendly elements)
- **Mobile**: 320px-767px (Stacked layout with hamburger navigation)

### Mobile Features
- Touch-friendly navigation
- Optimized form inputs
- Compressed layouts
- Fast loading times

## 🔒 Security Features

- **Password Hashing**: Argon2ID algorithm
- **Session Management**: Secure session handling
- **CSRF Protection**: Cross-site request forgery prevention
- **Input Validation**: Server-side and client-side validation
- **Rate Limiting**: Protection against brute force attacks
- **SQL Injection Prevention**: Prepared statements

## 🎯 Browser Compatibility

### Supported Browsers
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### Progressive Enhancement
- Core functionality works on older browsers
- Enhanced features for modern browsers
- Graceful degradation for unsupported features

## ⚡ Performance Optimizations

- **Minified Assets**: Compressed CSS and JavaScript
- **Image Optimization**: WebP format with fallbacks
- **Caching**: Browser and server-side caching
- **Lazy Loading**: Images and content loaded on demand
- **CDN Ready**: Assets can be served from CDN

## 🎮 Minecraft Integration

### Server Status
- Real-time player count
- Server version information
- MOTD (Message of the Day)
- Ping/latency measurement

### Player Data
- Minecraft UUID integration
- Player statistics tracking
- Achievement system
- Playtime monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- **Email**: <EMAIL>
- **Discord**: Join our community server
- **Documentation**: Check the wiki for detailed guides

## 🔄 Updates

### Version 1.0.0
- Initial release
- Complete authentication system
- Real-time server status
- Responsive design
- Dashboard functionality

---

**MineSea** - Dive into Adventure! 🌊
