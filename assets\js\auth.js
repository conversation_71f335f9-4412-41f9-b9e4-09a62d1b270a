// MineSea - Authentication JavaScript

class AuthManager {
    constructor() {
        this.loginForm = document.getElementById('login-form');
        this.registerForm = document.getElementById('register-form');
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        // Initialize login form
        if (this.loginForm) {
            this.initializeLoginForm();
        }
        
        // Initialize register form
        if (this.registerForm) {
            this.initializeRegisterForm();
        }
        
        // Initialize form validation
        this.initializeValidation();
        
        // Check if user is already logged in
        this.checkAuthStatus();
    }
    
    initializeLoginForm() {
        this.loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin();
        });
        
        // Add real-time validation
        const inputs = this.loginForm.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }
    
    initializeRegisterForm() {
        this.registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleRegister();
        });
        
        // Add real-time validation
        const inputs = this.registerForm.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
        
        // Password confirmation validation
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm-password');
        
        if (password && confirmPassword) {
            confirmPassword.addEventListener('input', () => {
                this.validatePasswordConfirmation(password.value, confirmPassword.value);
            });
        }
    }
    
    initializeValidation() {
        // Username validation
        const usernameInputs = document.querySelectorAll('input[name="username"]');
        usernameInputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateUsername(input.value, input);
            });
        });
        
        // Email validation
        const emailInputs = document.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateEmail(input.value, input);
            });
        });
        
        // Minecraft username validation
        const mcUsernameInput = document.getElementById('minecraft-username');
        if (mcUsernameInput) {
            mcUsernameInput.addEventListener('input', () => {
                this.validateMinecraftUsername(mcUsernameInput.value, mcUsernameInput);
            });
        }
    }
    
    async handleLogin() {
        if (this.isLoading) return;
        
        const formData = new FormData(this.loginForm);
        const username = formData.get('username');
        const password = formData.get('password');
        const remember = formData.get('remember') === 'on';
        
        // Validate inputs
        if (!this.validateLoginInputs(username, password)) {
            return;
        }
        
        this.setLoading(true);
        this.clearMessages();
        
        try {
            const response = await fetch('backend/api/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    remember: remember
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage('Login successful! Redirecting...');
                
                // Store user data
                localStorage.setItem('user', JSON.stringify(result.user));
                
                // Redirect after short delay
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                this.showErrorMessage(result.error || 'Login failed. Please try again.');
            }
            
        } catch (error) {
            console.error('Login error:', error);
            this.showErrorMessage('Network error. Please check your connection and try again.');
        } finally {
            this.setLoading(false);
        }
    }
    
    async handleRegister() {
        if (this.isLoading) return;
        
        const formData = new FormData(this.registerForm);
        const data = {
            username: formData.get('username'),
            email: formData.get('email'),
            minecraft_username: formData.get('minecraft_username'),
            password: formData.get('password'),
            confirm_password: formData.get('confirm_password'),
            terms: formData.get('terms') === 'on'
        };
        
        // Validate inputs
        if (!this.validateRegisterInputs(data)) {
            return;
        }
        
        this.setLoading(true);
        this.clearMessages();
        
        try {
            const response = await fetch('backend/api/register.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage(result.message || 'Registration successful! Please check your email for verification.');
                
                // Clear form
                this.registerForm.reset();
                
                // Redirect to login after delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);
            } else {
                if (result.errors && Array.isArray(result.errors)) {
                    result.errors.forEach(error => this.showErrorMessage(error));
                } else {
                    this.showErrorMessage(result.error || 'Registration failed. Please try again.');
                }
            }
            
        } catch (error) {
            console.error('Registration error:', error);
            this.showErrorMessage('Network error. Please check your connection and try again.');
        } finally {
            this.setLoading(false);
        }
    }
    
    validateLoginInputs(username, password) {
        let isValid = true;
        
        if (!username || username.trim().length < 3) {
            this.showFieldError('username', 'Username is required');
            isValid = false;
        }
        
        if (!password || password.length < 8) {
            this.showFieldError('password', 'Password is required');
            isValid = false;
        }
        
        return isValid;
    }
    
    validateRegisterInputs(data) {
        let isValid = true;
        
        // Username validation
        if (!this.validateUsername(data.username)) {
            isValid = false;
        }
        
        // Email validation
        if (!this.validateEmail(data.email)) {
            isValid = false;
        }
        
        // Minecraft username validation
        if (!this.validateMinecraftUsername(data.minecraft_username)) {
            isValid = false;
        }
        
        // Password validation
        if (!this.validatePassword(data.password)) {
            isValid = false;
        }
        
        // Password confirmation
        if (!this.validatePasswordConfirmation(data.password, data.confirm_password)) {
            isValid = false;
        }
        
        // Terms acceptance
        if (!data.terms) {
            this.showErrorMessage('You must accept the Terms of Service and Privacy Policy');
            isValid = false;
        }
        
        return isValid;
    }
    
    validateUsername(username, inputElement = null) {
        const input = inputElement || document.querySelector('input[name="username"]');
        
        if (!username || username.length < 3 || username.length > 50) {
            this.showFieldError('username', 'Username must be between 3 and 50 characters');
            return false;
        }
        
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            this.showFieldError('username', 'Username can only contain letters, numbers, and underscores');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    validateEmail(email, inputElement = null) {
        const input = inputElement || document.querySelector('input[type="email"]');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!email || !emailRegex.test(email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    validateMinecraftUsername(username, inputElement = null) {
        const input = inputElement || document.getElementById('minecraft-username');
        
        if (!username || username.length < 3 || username.length > 16) {
            this.showFieldError('minecraft-username', 'Minecraft username must be between 3 and 16 characters');
            return false;
        }
        
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            this.showFieldError('minecraft-username', 'Minecraft username can only contain letters, numbers, and underscores');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    validatePassword(password, inputElement = null) {
        const input = inputElement || document.getElementById('password');
        
        if (!password || password.length < 8) {
            this.showFieldError('password', 'Password must be at least 8 characters long');
            return false;
        }
        
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
            this.showFieldError('password', 'Password must contain at least one uppercase letter, one lowercase letter, and one number');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    validatePasswordConfirmation(password, confirmPassword) {
        const input = document.getElementById('confirm-password');
        
        if (password !== confirmPassword) {
            this.showFieldError('confirm-password', 'Passwords do not match');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    validateField(input) {
        const value = input.value;
        const name = input.name || input.id;
        
        switch (name) {
            case 'username':
                return this.validateUsername(value, input);
            case 'email':
                return this.validateEmail(value, input);
            case 'minecraft_username':
            case 'minecraft-username':
                return this.validateMinecraftUsername(value, input);
            case 'password':
                return this.validatePassword(value, input);
            case 'confirm_password':
            case 'confirm-password':
                const password = document.getElementById('password');
                return this.validatePasswordConfirmation(password ? password.value : '', value);
            default:
                return true;
        }
    }
    
    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        const inputElement = document.getElementById(fieldName) || document.querySelector(`input[name="${fieldName}"]`);
        
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
        
        if (inputElement) {
            inputElement.classList.add('error');
        }
    }
    
    clearFieldError(input) {
        if (!input) return;
        
        const fieldName = input.name || input.id;
        const errorElement = document.getElementById(`${fieldName}-error`);
        
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        }
        
        input.classList.remove('error');
    }
    
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }
    
    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.auth-message');
        existingMessages.forEach(msg => msg.remove());
        
        // Create new message
        const messageElement = document.createElement('div');
        messageElement.className = `auth-message ${type}-message show`;
        messageElement.textContent = message;
        
        // Insert at top of form
        const form = this.loginForm || this.registerForm;
        if (form) {
            form.insertBefore(messageElement, form.firstChild);
        }
        
        // Auto-remove error messages after 5 seconds
        if (type === 'error') {
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 5000);
        }
    }
    
    clearMessages() {
        const messages = document.querySelectorAll('.auth-message');
        messages.forEach(msg => msg.remove());
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        const form = this.loginForm || this.registerForm;
        const submitButton = form ? form.querySelector('button[type="submit"]') : null;
        
        if (form) {
            if (loading) {
                form.classList.add('loading');
            } else {
                form.classList.remove('loading');
            }
        }
        
        if (submitButton) {
            submitButton.disabled = loading;
            if (loading) {
                submitButton.dataset.originalText = submitButton.textContent;
                submitButton.textContent = 'Please wait...';
            } else {
                submitButton.textContent = submitButton.dataset.originalText || submitButton.textContent;
            }
        }
    }
    
    async checkAuthStatus() {
        try {
            const response = await fetch('backend/api/auth-check.php');
            const result = await response.json();
            
            if (result.authenticated) {
                // User is already logged in, redirect if on login/register page
                const currentPage = window.location.pathname.split('/').pop();
                if (currentPage === 'login.html' || currentPage === 'register.html') {
                    window.location.href = 'dashboard.html';
                }
            }
        } catch (error) {
            console.error('Auth check error:', error);
        }
    }
    
    async logout() {
        try {
            const response = await fetch('backend/api/logout.php', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                localStorage.removeItem('user');
                window.location.href = 'index.html';
            }
        } catch (error) {
            console.error('Logout error:', error);
            // Force logout on error
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }
    }
}

// Initialize authentication manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.authManager = new AuthManager();
});

// Global logout function
function logout() {
    if (window.authManager) {
        window.authManager.logout();
    }
}

// Export for use in other scripts
window.AuthManager = AuthManager;
