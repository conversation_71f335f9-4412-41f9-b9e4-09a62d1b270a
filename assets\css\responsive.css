/* MineSea - Responsive CSS */

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-container {
        padding: 1rem 1.5rem;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-image {
        order: -1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .community-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Navigation Mobile */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
        gap: 1rem;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    /* Hero Mobile */
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
    
    /* Typography Mobile */
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
    
    /* Sections Mobile */
    section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    /* Stats Grid Mobile */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    /* Features Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    /* Footer Mobile */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    /* Form Mobile */
    .contact-form {
        gap: 0.75rem;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 10px;
    }
    
    /* Button Mobile */
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .feature-card {
        padding: 1rem;
    }
    
    .news-section,
    .discord-section {
        padding: 1.5rem;
    }
    
    .contact-item {
        padding: 1rem;
    }
    
    .server-status {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .status-indicator {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
    
    .player-count {
        font-size: 0.8rem;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-title {
        font-size: 5rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding: 1rem 0;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    .hero-buttons {
        margin-bottom: 1rem;
    }
    
    .server-status {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-buttons,
    .contact-form,
    .footer {
        display: none;
    }
    
    .hero {
        min-height: auto;
        padding: 2rem 0;
        background: none;
        color: var(--text-primary);
    }
    
    .hero-title,
    .section-title {
        color: var(--text-primary);
    }
    
    section {
        padding: 1rem 0;
        break-inside: avoid;
    }
    
    .feature-card,
    .news-section,
    .discord-section {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .wave-animation {
        animation: none;
    }
    
    .status-dot {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-blue: #0052cc;
        --secondary-blue: #003d99;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #666666;
    }
    
    .btn-primary {
        border: 2px solid #000000;
    }
    
    .feature-card,
    .news-section,
    .discord-section,
    .contact-item {
        border: 2px solid var(--border-color);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --background-light: #1a1a1a;
        --background-gray: #2a2a2a;
        --border-color: #404040;
    }
    
    .navbar {
        background: rgba(26, 26, 26, 0.95);
        border-bottom-color: var(--border-color);
    }
    
    .feature-card,
    .news-section,
    .discord-section,
    .contact-item,
    .stat-item {
        background: var(--background-gray);
        border: 1px solid var(--border-color);
    }
    
    .form-group input,
    .form-group textarea {
        background: var(--background-gray);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: var(--text-secondary);
    }
}
