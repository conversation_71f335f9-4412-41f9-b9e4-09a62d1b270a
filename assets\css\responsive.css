/* MineSea - Modern Responsive CSS */

/* Large Desktop */
@media (min-width: 1400px) {
    .container-2xl {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 4rem;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-content {
        gap: 3rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .server-info-content {
        gap: 3rem;
    }

    .info-main {
        padding-right: 1rem;
    }
}

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 1rem;
    }

    .nav-container {
        padding: 0 1.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-5xl);
    }

    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }

    .server-info-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .info-main {
        padding-right: 0;
    }

    .info-stats {
        position: static;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Navigation Mobile */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 4rem;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: var(--blur-lg);
        width: 100%;
        height: calc(100vh - 4rem);
        text-align: center;
        transition: var(--transition-slow);
        box-shadow: var(--shadow-xl);
        padding: 2rem 0;
        gap: 1.5rem;
        z-index: var(--z-modal);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        font-size: var(--font-size-lg);
        padding: 1rem 0;
    }

    .hamburger {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        gap: 0.25rem;
    }

    .bar {
        width: 1.5rem;
        height: 2px;
        background: var(--dark-navy);
        transition: var(--transition);
        border-radius: 1px;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(6px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
    }

    /* Hero Mobile */
    .hero {
        min-height: 100vh;
        padding-top: 4rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        line-height: 1.1;
    }

    .hero-description {
        font-size: var(--font-size-base);
        max-width: 100%;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 280px;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: var(--font-size-lg);
    }

    .stat-label {
        font-size: var(--font-size-xs);
    }

    /* Server Preview Mobile */
    .preview-window {
        margin: 0 1rem;
    }

    .window-content {
        padding: 1.5rem 1rem;
    }

    /* Typography Mobile */
    .heading-1,
    h1 { font-size: var(--font-size-4xl); }
    .heading-2,
    h2 { font-size: var(--font-size-3xl); }
    .heading-3,
    h3 { font-size: var(--font-size-2xl); }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .section-description {
        font-size: var(--font-size-base);
    }

    /* Sections Mobile */
    .section {
        padding: 3rem 0;
    }

    .section-sm {
        padding: 2rem 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    /* Features Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .feature-badge {
        align-self: center;
    }

    /* Server Info Mobile */
    .server-info-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .info-highlights {
        gap: 1rem;
    }

    .highlight-item {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .highlight-item:hover {
        transform: translateY(-2px);
    }

    .server-actions {
        flex-direction: column;
        align-items: center;
    }

    .server-actions .btn {
        width: 100%;
        max-width: 280px;
    }

    .stats-container {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card-large {
        padding: 1rem;
    }

    /* Footer Mobile */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    /* Button Mobile */
    .btn {
        min-height: 2.5rem;
    }

    .btn-lg {
        min-height: 3rem;
        padding: 0.75rem 1.5rem;
        font-size: var(--font-size-base);
    }

    .btn-xl {
        min-height: 3.5rem;
        padding: 1rem 2rem;
        font-size: var(--font-size-lg);
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .feature-card {
        padding: 1rem;
    }
    
    .news-section,
    .discord-section {
        padding: 1.5rem;
    }
    
    .contact-item {
        padding: 1rem;
    }
    
    .server-status {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .status-indicator {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
    
    .player-count {
        font-size: 0.8rem;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-title {
        font-size: 5rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding: 1rem 0;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    .hero-buttons {
        margin-bottom: 1rem;
    }
    
    .server-status {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-buttons,
    .contact-form,
    .footer {
        display: none;
    }
    
    .hero {
        min-height: auto;
        padding: 2rem 0;
        background: none;
        color: var(--text-primary);
    }
    
    .hero-title,
    .section-title {
        color: var(--text-primary);
    }
    
    section {
        padding: 1rem 0;
        break-inside: avoid;
    }
    
    .feature-card,
    .news-section,
    .discord-section {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .wave-animation {
        animation: none;
    }
    
    .status-dot {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-blue: #0052cc;
        --secondary-blue: #003d99;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #666666;
    }
    
    .btn-primary {
        border: 2px solid #000000;
    }
    
    .feature-card,
    .news-section,
    .discord-section,
    .contact-item {
        border: 2px solid var(--border-color);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --background-light: #1a1a1a;
        --background-gray: #2a2a2a;
        --border-color: #404040;
    }
    
    .navbar {
        background: rgba(26, 26, 26, 0.95);
        border-bottom-color: var(--border-color);
    }
    
    .feature-card,
    .news-section,
    .discord-section,
    .contact-item,
    .stat-item {
        background: var(--background-gray);
        border: 1px solid var(--border-color);
    }
    
    .form-group input,
    .form-group textarea {
        background: var(--background-gray);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: var(--text-secondary);
    }
}
