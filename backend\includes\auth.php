<?php
/**
 * MineSea Authentication System
 * 
 * Handles user authentication, session management, and security
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Authentication Manager Class
 */
class AuthManager {
    private $db;
    private $sessionLifetime;
    
    public function __construct() {
        $this->db = DatabaseManager::getInstance();
        $this->sessionLifetime = Config::get('security.session_lifetime', 86400);
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            $this->startSecureSession();
        }
    }
    
    /**
     * Start secure session
     */
    private function startSecureSession() {
        // Configure session settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        session_start();
        
        // Regenerate session ID periodically
        if (!isset($_SESSION['created'])) {
            $_SESSION['created'] = time();
        } elseif (time() - $_SESSION['created'] > 1800) { // 30 minutes
            session_regenerate_id(true);
            $_SESSION['created'] = time();
        }
    }
    
    /**
     * Register new user
     */
    public function register($username, $email, $password, $minecraftUsername) {
        try {
            // Validate input
            $validation = $this->validateRegistration($username, $email, $password, $minecraftUsername);
            if (!$validation['valid']) {
                return ['success' => false, 'errors' => $validation['errors']];
            }
            
            // Check if user already exists
            if ($this->userExists($username, $email, $minecraftUsername)) {
                return ['success' => false, 'errors' => ['User already exists']];
            }
            
            // Hash password
            $passwordHash = DatabaseUtils::hashPassword($password);
            
            // Generate verification token
            $verificationToken = DatabaseUtils::generateToken();
            
            // Begin transaction
            $this->db->beginTransaction();
            
            try {
                // Insert user
                $userId = $this->createUser([
                    'username' => $username,
                    'email' => $email,
                    'password_hash' => $passwordHash,
                    'minecraft_username' => $minecraftUsername,
                    'email_verification_token' => $verificationToken,
                    'created_at' => DatabaseUtils::formatDate()
                ]);
                
                // Create player profile
                $this->createPlayerProfile($userId, $minecraftUsername);
                
                // Create player statistics
                $this->createPlayerStatistics($userId);
                
                // Send verification email
                $this->sendVerificationEmail($email, $verificationToken);
                
                $this->db->commit();
                
                return [
                    'success' => true,
                    'user_id' => $userId,
                    'message' => 'Registration successful. Please check your email for verification.'
                ];
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['Registration failed. Please try again.']];
        }
    }
    
    /**
     * Login user
     */
    public function login($usernameOrEmail, $password, $rememberMe = false) {
        try {
            // Check rate limiting
            if (!$this->checkLoginRateLimit()) {
                return ['success' => false, 'error' => 'Too many login attempts. Please try again later.'];
            }
            
            // Find user
            $user = $this->findUserByUsernameOrEmail($usernameOrEmail);
            if (!$user) {
                $this->recordFailedLogin();
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            // Verify password
            if (!DatabaseUtils::verifyPassword($password, $user['password_hash'])) {
                $this->recordFailedLogin();
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            // Check if user is active
            if (!$user['is_active']) {
                return ['success' => false, 'error' => 'Account is disabled'];
            }
            
            // Create session
            $sessionId = $this->createSession($user['id'], $rememberMe);
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['session_id'] = $sessionId;
            
            // Clear failed login attempts
            $this->clearFailedLogins();
            
            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'display_name' => $user['display_name']
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['session_id'])) {
            $this->deleteSession($_SESSION['session_id']);
        }
        
        // Clear session
        session_unset();
        session_destroy();
        
        // Start new session
        $this->startSecureSession();
        
        return ['success' => true];
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_id'])) {
            return false;
        }
        
        // Verify session in database
        $session = $this->getSession($_SESSION['session_id']);
        if (!$session || $session['user_id'] != $_SESSION['user_id']) {
            return false;
        }
        
        // Check if session has expired
        if (strtotime($session['expires_at']) < time()) {
            $this->deleteSession($_SESSION['session_id']);
            return false;
        }
        
        // Update last activity
        $this->updateSessionActivity($_SESSION['session_id']);
        
        return true;
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        return $this->getUserById($_SESSION['user_id']);
    }
    
    /**
     * Check user role
     */
    public function hasRole($role) {
        if (!$this->isAuthenticated()) {
            return false;
        }
        
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        return $this->hasRole('admin');
    }
    
    /**
     * Check if user is moderator or admin
     */
    public function isModerator() {
        return $this->hasRole('moderator') || $this->hasRole('admin');
    }
    
    /**
     * Validate registration data
     */
    private function validateRegistration($username, $email, $password, $minecraftUsername) {
        $errors = [];
        
        // Username validation
        if (empty($username) || strlen($username) < 3 || strlen($username) > 50) {
            $errors[] = 'Username must be between 3 and 50 characters';
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'Username can only contain letters, numbers, and underscores';
        }
        
        // Email validation
        if (!DatabaseUtils::isValidEmail($email)) {
            $errors[] = 'Invalid email address';
        }
        
        // Password validation
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
        }
        
        // Minecraft username validation
        if (empty($minecraftUsername) || strlen($minecraftUsername) < 3 || strlen($minecraftUsername) > 16) {
            $errors[] = 'Minecraft username must be between 3 and 16 characters';
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $minecraftUsername)) {
            $errors[] = 'Minecraft username can only contain letters, numbers, and underscores';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Check if user exists
     */
    private function userExists($username, $email, $minecraftUsername) {
        $result = $this->db->fetchOne(
            "SELECT id FROM users WHERE username = ? OR email = ? OR minecraft_username = ?",
            [$username, $email, $minecraftUsername]
        );
        
        return $result !== false;
    }
    
    /**
     * Create new user
     */
    private function createUser($userData) {
        $this->db->execute(
            "INSERT INTO users (username, email, password_hash, minecraft_username, email_verification_token, created_at) 
             VALUES (?, ?, ?, ?, ?, ?)",
            [
                $userData['username'],
                $userData['email'],
                $userData['password_hash'],
                $userData['minecraft_username'],
                $userData['email_verification_token'],
                $userData['created_at']
            ]
        );
        
        return $this->db->getLastInsertId();
    }
    
    /**
     * Create player profile
     */
    private function createPlayerProfile($userId, $minecraftUsername) {
        // Get Minecraft UUID (simplified - in production, use Mojang API)
        $uuid = DatabaseUtils::generateUUID();
        
        $this->db->execute(
            "INSERT INTO player_profiles (user_id, minecraft_uuid, join_date) VALUES (?, ?, ?)",
            [$userId, $uuid, date('Y-m-d')]
        );
    }
    
    /**
     * Create player statistics
     */
    private function createPlayerStatistics($userId) {
        $this->db->execute(
            "INSERT INTO player_statistics (user_id) VALUES (?)",
            [$userId]
        );
    }
    
    /**
     * Find user by username or email
     */
    private function findUserByUsernameOrEmail($usernameOrEmail) {
        return $this->db->fetchOne(
            "SELECT * FROM users WHERE username = ? OR email = ?",
            [$usernameOrEmail, $usernameOrEmail]
        );
    }
    
    /**
     * Get user by ID
     */
    private function getUserById($userId) {
        return $this->db->fetchOne(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * Create session
     */
    private function createSession($userId, $rememberMe = false) {
        $sessionId = DatabaseUtils::generateToken(64);
        $expiresAt = date('Y-m-d H:i:s', time() + ($rememberMe ? 30 * 24 * 3600 : $this->sessionLifetime));
        
        $this->db->execute(
            "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)",
            [
                $sessionId,
                $userId,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $expiresAt
            ]
        );
        
        return $sessionId;
    }
    
    /**
     * Get session
     */
    private function getSession($sessionId) {
        return $this->db->fetchOne(
            "SELECT * FROM user_sessions WHERE id = ?",
            [$sessionId]
        );
    }
    
    /**
     * Update session activity
     */
    private function updateSessionActivity($sessionId) {
        $this->db->execute(
            "UPDATE user_sessions SET last_activity = CURRENT_TIMESTAMP WHERE id = ?",
            [$sessionId]
        );
    }
    
    /**
     * Delete session
     */
    private function deleteSession($sessionId) {
        $this->db->execute(
            "DELETE FROM user_sessions WHERE id = ?",
            [$sessionId]
        );
    }
    
    /**
     * Update last login
     */
    private function updateLastLogin($userId) {
        $this->db->execute(
            "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * Check login rate limit
     */
    private function checkLoginRateLimit() {
        // Simple rate limiting based on IP
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $window = Config::get('security.login_attempts_window', 900);
        $limit = Config::get('security.login_attempts_limit', 5);
        
        // This would be implemented with a proper rate limiting system
        // For now, return true
        return true;
    }
    
    /**
     * Record failed login attempt
     */
    private function recordFailedLogin() {
        // Log failed login attempt
        error_log("Failed login attempt from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    }
    
    /**
     * Clear failed login attempts
     */
    private function clearFailedLogins() {
        // Clear failed login attempts for this IP
    }
    
    /**
     * Send verification email
     */
    private function sendVerificationEmail($email, $token) {
        // Email sending would be implemented here
        // For now, just log the token
        error_log("Verification token for $email: $token");
    }
}

// Initialize authentication manager
$auth = new AuthManager();

?>
