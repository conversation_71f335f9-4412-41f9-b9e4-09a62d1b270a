<?php
/**
 * MineSea News API
 * 
 * Provides news and announcements data
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $db = DatabaseManager::getInstance();
    
    // Get query parameters
    $limit = min(50, max(1, intval($_GET['limit'] ?? 10)));
    $offset = max(0, intval($_GET['offset'] ?? 0));
    $category = $_GET['category'] ?? null;
    $featured = isset($_GET['featured']) ? filter_var($_GET['featured'], FILTER_VALIDATE_BOOLEAN) : null;
    
    // Build query
    $whereConditions = ['n.is_published = 1'];
    $params = [];
    
    if ($category) {
        $whereConditions[] = 'n.category = ?';
        $params[] = $category;
    }
    
    if ($featured !== null) {
        $whereConditions[] = 'n.is_featured = ?';
        $params[] = $featured ? 1 : 0;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get news items
    $sql = "
        SELECT 
            n.id,
            n.title,
            n.excerpt,
            n.category,
            n.is_featured,
            n.image_url,
            n.published_at,
            n.created_at,
            u.display_name as author_name,
            u.username as author_username
        FROM news n
        JOIN users u ON n.author_id = u.id
        WHERE $whereClause
        ORDER BY n.is_featured DESC, n.published_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $news = $db->fetchAll($sql, $params);
    
    // Get total count for pagination
    $countSql = "
        SELECT COUNT(*) as total
        FROM news n
        WHERE $whereClause
    ";
    
    $countParams = array_slice($params, 0, -2); // Remove limit and offset
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];
    
    // Format the response
    $response = [
        'success' => true,
        'data' => $news,
        'pagination' => [
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total
        ]
    ];
    
    // Add sample news if none exist
    if (empty($news) && $offset === 0) {
        $sampleNews = [
            [
                'id' => 1,
                'title' => 'Welcome to MineSea!',
                'excerpt' => 'Dive into our brand new ocean-themed Minecraft server with custom islands, quests, and adventures waiting for you.',
                'category' => 'announcement',
                'is_featured' => true,
                'image_url' => null,
                'published_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'author_name' => 'MineSea Team',
                'author_username' => 'admin'
            ],
            [
                'id' => 2,
                'title' => 'New Fishing Tournament Event',
                'excerpt' => 'Join our weekly fishing tournament every Saturday! Compete with other players and win exclusive rewards.',
                'category' => 'event',
                'is_featured' => false,
                'image_url' => null,
                'published_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'author_name' => 'MineSea Team',
                'author_username' => 'admin'
            ],
            [
                'id' => 3,
                'title' => 'Server Update v1.2.0',
                'excerpt' => 'New features include enhanced protection systems, improved economy, and bug fixes for better gameplay experience.',
                'category' => 'update',
                'is_featured' => false,
                'image_url' => null,
                'published_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'author_name' => 'MineSea Team',
                'author_username' => 'admin'
            ]
        ];
        
        $response['data'] = $sampleNews;
        $response['pagination']['total'] = count($sampleNews);
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("News API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to retrieve news'
    ]);
}

?>
